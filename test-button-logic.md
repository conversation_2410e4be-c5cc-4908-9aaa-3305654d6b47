# Apply/Reject 按钮显示逻辑测试

## 修改后的逻辑

### 1. 按钮显示条件
```vue
<div v-if="skill.checked" class="flex items-center gap-1 mr-2">
```

### 2. checkTableRendered 函数
```javascript
const checkTableRendered = (index: number, isUserGenerated: boolean = false) => {
  const skill = trainingNeed.value.content[index]
  const parsedData = parsedTableData.value[index] || []
  const hasData = parsedData.length > 0
  const notGenerating = generatingIndex.value !== index

  // 只有在表格渲染完成、有数据且是用户刚刚生成的内容时才设置为true
  const shouldCheck = hasData && notGenerating && isUserGenerated
  skill.checked = shouldCheck
}
```

### 3. 调用场景

#### 场景1：从编辑页面加载已有数据
- `getTrainingNeed()` 函数中不再调用 `checkTableRendered()`
- 已有数据的 `checked` 状态保持为 `false`
- **结果：不显示 Apply/Reject 按钮** ✅

#### 场景2：用户点击生成按钮
- 流式输出完成后调用 `checkTableRendered(index, true)`
- `isUserGenerated = true`
- 如果有数据且不在生成中，`checked` 设置为 `true`
- **结果：显示 Apply/Reject 按钮** ✅

#### 场景3：用户点击 Apply 或 Reject
- `applyContent()` 或 `rejectContent()` 将 `checked` 设置为 `false`
- **结果：隐藏 Apply/Reject 按钮** ✅

## 测试用例

### 用例1：编辑页面加载
1. 进入编辑页面
2. 页面加载已有的培训需求数据
3. 表格正常显示课程数据
4. **预期：不显示 Apply/Reject 按钮**

### 用例2：新增页面生成内容
1. 进入新增页面
2. 填写技能名称和培训类型
3. 点击生成按钮
4. 等待流式输出完成
5. **预期：显示 Apply/Reject 按钮**

### 用例3：编辑页面重新生成
1. 进入编辑页面（已有数据，不显示按钮）
2. 点击生成按钮重新生成内容
3. 等待流式输出完成
4. **预期：显示 Apply/Reject 按钮**

### 用例4：点击按钮后隐藏
1. 生成内容后显示按钮
2. 点击 Apply 或 Reject 按钮
3. **预期：按钮消失**

## 关键改动总结

1. **移除了编辑页面加载时的 `checkTableRendered` 调用**
2. **为 `checkTableRendered` 添加了 `isUserGenerated` 参数**
3. **只有用户生成的内容才会显示 Apply/Reject 按钮**
4. **保持了其他所有逻辑不变**
