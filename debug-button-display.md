# 调试按钮显示问题

## 问题描述
表格渲染完成后，Apply 和 Reject 按钮组没有显示。

## 可能的原因

### 1. checkTableRendered 没有被调用
- 在 `onMessage` 中检测到完整内容时没有调用
- 在 `onClose` 中的条件判断可能不满足

### 2. checkTableRendered 被调用但条件不满足
- `hasData` 为 false（解析失败）
- `notGenerating` 为 false（仍在生成状态）
- `isUserGenerated` 为 false（参数传递错误）

### 3. skill.checked 设置成功但模板没有响应
- Vue 响应性问题
- 模板条件判断问题

## 修复方案

### 修复1：确保 checkTableRendered 被调用
```javascript
// 在 onMessage 中检测到完整内容时调用
const finalContent = extractFinalContent(rawContent)
if (finalContent && finalContent.trim() !== '') {
  skill.contentCatalog = finalContent
  skill.fullContentCatalog = finalContent
  console.log('最终内容已一次性设置到textarea')
  
  // 检查表格是否渲染完成并有数据，设置checked状态（用户生成的内容）
  setTimeout(() => {
    checkTableRendered(index, true)
  }, 100) // 延迟一点时间确保DOM更新完成
}
```

### 修复2：在 onClose 中无条件调用
```javascript
// 无论如何都检查一次表格渲染状态，确保按钮能正确显示
setTimeout(() => {
  checkTableRendered(index, true)
}, 200) // 稍微延长延迟时间确保所有处理完成
```

## 调试步骤

1. **检查控制台日志**：
   - 查看是否有 "检查技能 X 表格渲染状态:" 的日志
   - 查看各个条件的值：hasData, notGenerating, isUserGenerated
   - 查看最终的 shouldCheck 值

2. **检查数据解析**：
   - 查看 parsedTableData 计算属性的结果
   - 确认 parseContentCatalog 函数正确解析了内容

3. **检查生成状态**：
   - 确认 generatingIndex 在流式输出完成后被正确重置

4. **检查模板响应**：
   - 在浏览器开发者工具中检查 skill.checked 的值
   - 确认模板条件 `v-if="skill.checked"` 正确工作

## 预期行为

1. 用户点击生成按钮
2. 流式输出开始，generatingIndex 设置为当前索引
3. 流式输出过程中，skill.checked 为 false，按钮不显示
4. 流式输出完成，调用 checkTableRendered(index, true)
5. checkTableRendered 检查条件：
   - hasData: true（解析成功）
   - notGenerating: true（generatingIndex !== index）
   - isUserGenerated: true（参数传入）
6. 设置 skill.checked = true
7. 模板响应，显示按钮组
