<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Parse Content Catalog</title>
</head>
<body>
    <h1>Test Parse Content Catalog Function</h1>
    <div id="test-results"></div>

    <script>
        // 复制 parseContentCatalog 函数进行测试
        const parseContentCatalog = (contentCatalog) => {
            console.log('开始解析内容目录:', contentCatalog)
            
            if (!contentCatalog || contentCatalog.trim() === '') {
                console.log('内容为空，返回空数组')
                return []
            }

            const trimmedContent = contentCatalog.trim()
            console.log('处理后的内容:', trimmedContent)

            // 放宽检查条件，允许更多格式的内容
            if (trimmedContent.length < 5) {
                console.log('内容太短，返回空数组')
                return []
            }

            try {
                let parsedData

                // 尝试多种解析方式
                if (trimmedContent.startsWith('[') || trimmedContent.startsWith('{')) {
                    // 直接解析JSON
                    console.log('尝试直接解析JSON')
                    parsedData = JSON.parse(trimmedContent)
                } else {
                    // 尝试从文本中提取JSON数组
                    console.log('尝试从文本中提取JSON数组')
                    const arrayMatch = trimmedContent.match(/\[[\s\S]*\]/)
                    if (arrayMatch) {
                        console.log('找到数组匹配:', arrayMatch[0])
                        parsedData = JSON.parse(arrayMatch[0])
                    } else {
                        // 尝试查找对象数组的模式
                        const objectArrayMatch = trimmedContent.match(/\{[\s\S]*\}/)
                        if (objectArrayMatch) {
                            console.log('找到对象匹配，尝试包装为数组')
                            // 如果找到对象，尝试将其包装为数组
                            const objectStr = objectArrayMatch[0]
                            // 检查是否是多个对象用逗号分隔
                            if (objectStr.includes('},{')) {
                                parsedData = JSON.parse(`[${objectStr}]`)
                            } else {
                                parsedData = [JSON.parse(objectStr)]
                            }
                        } else {
                            console.log('未找到有效的JSON结构')
                            return []
                        }
                    }
                }

                console.log('初步解析结果:', parsedData)

                // 如果解析结果是对象且包含 course_recommendations 字段
                if (parsedData && typeof parsedData === 'object' && parsedData.course_recommendations) {
                    console.log('提取 course_recommendations 字段')
                    parsedData = parsedData.course_recommendations
                }

                // 确保结果是数组
                if (!Array.isArray(parsedData)) {
                    console.log('结果不是数组，尝试转换')
                    if (typeof parsedData === 'object' && parsedData !== null) {
                        // 如果是单个对象，包装为数组
                        parsedData = [parsedData]
                    } else {
                        console.log('无法转换为数组，返回空数组')
                        return []
                    }
                }

                console.log('最终解析的数组:', parsedData)

                // 确保每个对象都有必要的字段，支持多种字段名称
                const result = parsedData.map((item, index) => {
                    console.log(`处理数组项 ${index}:`, item)
                    return {
                        courseName: item['Course Name'] || item.courseName || item.name || item.title || 'N/A',
                        duration: item['Duration'] || item.duration || item.time || 'N/A',
                        level: item['Skill Level'] || item.level || item.difficulty || 'N/A',
                        description: item['Detailed Content/Covered Topics'] || item['Key Skills Gained'] || item.description || item.desc || item.summary || 'N/A'
                    }
                })

                console.log('最终表格数据:', result)
                return result
            } catch (error) {
                console.error('JSON解析失败:', error.message, '原始内容:', contentCatalog)
                return []
            }
        }

        // 测试用例
        const testCases = [
            // 测试用例1：标准JSON数组
            `[
                {
                    "Course Name": "JavaScript Fundamentals",
                    "Duration": "40 hours",
                    "Skill Level": "Beginner",
                    "Detailed Content/Covered Topics": "Basic syntax, variables, functions"
                }
            ]`,
            
            // 测试用例2：单个对象
            `{
                "Course Name": "Vue.js Advanced",
                "Duration": "30 hours", 
                "Skill Level": "Advanced",
                "Detailed Content/Covered Topics": "Composition API, state management"
            }`,
            
            // 测试用例3：包含course_recommendations的对象
            `{
                "course_recommendations": [
                    {
                        "courseName": "React Basics",
                        "duration": "25 hours",
                        "level": "Intermediate",
                        "description": "Component lifecycle, hooks"
                    }
                ]
            }`,
            
            // 测试用例4：空内容
            '',
            
            // 测试用例5：无效JSON
            'invalid json content'
        ]

        // 运行测试
        const resultsDiv = document.getElementById('test-results')
        
        testCases.forEach((testCase, index) => {
            console.log(`\n=== 测试用例 ${index + 1} ===`)
            const result = parseContentCatalog(testCase)
            
            const testDiv = document.createElement('div')
            testDiv.innerHTML = `
                <h3>测试用例 ${index + 1}</h3>
                <p><strong>输入:</strong> <code>${testCase.substring(0, 100)}${testCase.length > 100 ? '...' : ''}</code></p>
                <p><strong>输出:</strong> ${result.length > 0 ? `${result.length} 条记录` : '空数组'}</p>
                <p><strong>详细结果:</strong> <pre>${JSON.stringify(result, null, 2)}</pre></p>
                <hr>
            `
            resultsDiv.appendChild(testDiv)
        })
    </script>
</body>
</html>
