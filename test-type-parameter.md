# Type 参数传递测试

## 修改内容

### 1. 修改 `toEdit` 函数（index.vue）
```javascript
/** 跳转至编辑需求页面 */
const toEdit = (id: number, type: string) => {
  router.push({
    path: `/edp/training-need/edit/${id}`,
    query: { type }
  })
}
```

### 2. 修改 `currentMode` 计算属性（AddTrainingNeed.vue）
```javascript
// 根据路由自动判断模式和ID
const currentMode = computed(() => {
  if (props.type) return props.type
  // 优先使用查询参数中的 type
  if (route.query.type) return route.query.type as 'add' | 'edit' | 'view'
  return route.name === 'EditTrainingNeed' ? 'edit' : 'add'
})
```

## 工作流程

1. **用户点击编辑按钮**：
   - 调用 `toEdit(trainingNeed.id, 'edit')`
   - 跳转到 `/edp/training-need/edit/123?type=edit`

2. **AddTrainingNeed 组件接收参数**：
   - `route.query.type` 为 `'edit'`
   - `currentMode` 计算属性返回 `'edit'`

3. **组件根据 type 调整行为**：
   - 页面标题显示 "Edit Training Need"
   - 按钮显示逻辑根据 `currentMode` 调整

## 测试用例

### 用例1：通过路由跳转（带查询参数）
- URL: `/edp/training-need/edit/123?type=edit`
- 预期：`currentMode.value` 为 `'edit'`

### 用例2：通过 props 传递
- Props: `{ type: 'view', id: 123 }`
- 预期：`currentMode.value` 为 `'view'`（props 优先级更高）

### 用例3：仅通过路由名称判断
- URL: `/edp/training-need/edit/123`（无查询参数）
- 预期：`currentMode.value` 为 `'edit'`（根据路由名称判断）

### 用例4：添加页面
- URL: `/edp/training-need/add`
- 预期：`currentMode.value` 为 `'add'`

## 优先级顺序

1. **Props.type**（最高优先级）
2. **route.query.type**（查询参数）
3. **route.name**（路由名称，默认逻辑）

## 调试方法

在 AddTrainingNeed.vue 中添加调试日志：
```javascript
console.log('Route query:', route.query)
console.log('Props type:', props.type)
console.log('Current mode:', currentMode.value)
```

## 预期效果

现在当用户点击编辑按钮时：
1. URL 会包含 `?type=edit` 查询参数
2. 组件能正确识别为编辑模式
3. 相关的按钮显示逻辑会根据模式调整
