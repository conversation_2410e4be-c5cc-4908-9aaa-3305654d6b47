import type { App } from 'vue'
import { hasRole } from './permission/hasRole'
import { hasPermi } from './permission/hasPermi'
import LoadingDirective from './loading'
// import InfiniteScrollDirective from './infiniteScroll'

/**
 * 导出指令：v-xxx
 * @methods hasRole 用户权限，用法: v-hasRole
 * @methods hasPermi 按钮权限，用法: v-hasPermi
 */
export const setupAuth = (app: App<Element>) => {
  hasRole(app)
  hasPermi(app)
}

/**
 * 全局loading 指令: v-loading
 */
export const setupLoading = (app: App<Element>) => {
  // 注册 v-loading 指令
  app.directive('loading', LoadingDirective)
}
// export const setupInfiniteScroll = (app: App<Element>) => {
//   app.directive('infinite', InfiniteScrollDirective)
// }
/**
 * 导出指令：v-mountedFocus
 */
export const setupMountedFocus = (app: App<Element>) => {
  app.directive('mountedFocus', {
    mounted(el) {
      el.focus()
    }
  })
}
