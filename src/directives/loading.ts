import { Directive, DirectiveBinding } from 'vue'
import LoadingMaskComponent from '@/components/Loading/LoadingMask.vue'
import { createApp } from 'vue'

const MASK_OPACITY = 0.8

const LoadingDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    // 1. 解析绑定值
    const { value = true, opacity = MASK_OPACITY } =
      typeof binding.value === 'object'
        ? binding.value
        : { value: binding.value, opacity: MASK_OPACITY }

    // 2. 设置目标元素的定位
    el.style.position = 'relative'

    // 3. 创建容器元素
    const loadingContainer = document.createElement('div')
    loadingContainer.style.position = 'absolute'
    loadingContainer.style.width = '100%'
    loadingContainer.style.height = '100%'
    loadingContainer.style.zIndex = '10'
    loadingContainer.style.top = '0'
    loadingContainer.style.left = '0'
    loadingContainer.style.pointerEvents = 'none'

    // 4. 创建并挂载组件
    const app = createApp(LoadingMaskComponent, {
      show: value,
      opacity: opacity
    })
    el.appendChild(loadingContainer)
    app.mount(loadingContainer)
    el._loadingInstance = app
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    // 直接调用指令的 unmounted 方法，而不是通过 this
    LoadingDirective.unmounted(el)
    LoadingDirective.mounted(el, binding)
  },

  unmounted(el: HTMLElement) {
    // 清理逻辑
    el._loadingInstance?.unmount()
    el.removeChild(el.lastChild as Node)
    delete el._loadingInstance
  }
}

export default LoadingDirective