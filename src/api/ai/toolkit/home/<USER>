import request from '@/config/axios'

/* Union History VO */
export interface getUnionHistoryVO {
  pageNo: string
  pageSize: string
  bizType: number
  bizId: number
  status: number
  fileType: string
}

export interface UnionHistoryPageReqVO {
  bizType: number
  bizId: number
  status: number
  fileId: number
  fileName: string
  fileType: string
  fileSize: number
  fileUrl: string
  createTime: string
  msg: string
}

export type PageRequest = Pick<UnionHistoryPageReqVO, 'pageNo' | 'pageSize'> // 只包含 pageNo 和 pageSize

export interface CommonResultPageResultAppUnionHistoryRespVO {
  list: UnionHistoryPageReqVO[]
  total: number
}

export interface FileInfo {
  fileName: string
  fileType: string
  fileSize: number
  fileId: string
  fileUrl: string
  createTime: string
}

export interface DeleteUnionHistoryVO {
  bizType: number
  bizId: number
}

/* Union History API */
export const HomeApi = {
  getUnionHistory: async (data: getUnionHistoryVO) => {
    return await request.appGet({ url: `/ai/union/page`, data })
  },

  getUnionHistoryPage: async (params: UnionHistoryPageReqVO | PageRequest) => {
    return await request.appGet<CommonResultPageResultAppUnionHistoryRespVO>({
      url: '/ai/union/page',
      params
    })
  },

  deleteUnionHistory: async (data: DeleteUnionHistoryVO) => {
    return await request.appPost({ url: `/ai/union/delete`, data })
  }
}
