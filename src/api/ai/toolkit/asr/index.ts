import request from '@/config/axios'

// AI 音乐 VO
export interface CreateAsrVO {
  fileId: string
  language: string
  model: string
  prompt: string
}
export interface TranscriptionsVO {
  startTime: string
  endTime: string
  content: string
  translation: string
}
export interface FileInfo {
  fileName: string
  fileType: string
  fileSize: number
  fileId: string
  fileUrl: string
}
export interface AsrRespVO {
  id: number
  status: number
  errorMessage: string
  fileInfo: FileInfo
  lang: string
  asrContent: {
    model: string // 转写的模型
    language: string // 转写的语言
    contents: TranscriptionsVO[]
    translate: number // 双语翻译开关 1.原语言 2.目标语言 3.双语
    transLanguage: string // 翻译后的语言
    transType: number // 翻译类型
    transModel: string // 翻译模型
    prompt: string // 提示词
  }
  createTime: string
}
export interface UpdateAsrVO {
  id: number
  translate?: number // 双语翻译开关 1.原语言 2.目标语言 3.双语
  transLanguage?: string // 翻译后的语言
  transType?: number // 翻译类型
  transModel?: string // 翻译模型
  contents?: TranscriptionsVO[]
  bizType?: number
}
// Asr API
export const AsrApi = {
  // 创建转写
  createTranscription: async (data: CreateAsrVO) => {
    return await request.appPost({ url: `/ai/asr/create`, data })
  },

  getTranscription: async (id: number) => {
    return await request.appGet({ url: `/ai/asr/get?id=` + id })
  },

  updateTranscription: async (data: UpdateAsrVO) => {
    return await request.appPut({ url: `/ai/asr/update`, data })
  },

  getTranscriptionPage: async (params: any) => {
    return await request.appGet({ url: `/ai/asr/page`, params })
  },

  getModelOptionList: async (bizType: number) => {
    return await request.appGet({ url: `/ai/translate/model/simple-list?bizType=` + bizType })
  }
}
