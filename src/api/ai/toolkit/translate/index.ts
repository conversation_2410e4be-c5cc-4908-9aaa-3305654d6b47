import request from '@/config/axios'

export interface BasicTranslate {
  originLanguage: string
  targetLanguage: string
  model: string
  transType: number
}
export interface FileInfo {
  fileName: string
  fileType: string
  fileSize: number
  fileId: string
  fileUrl: string
}
export interface TranslateWordsReqVO extends BasicTranslate {
  content: string
  record?: boolean
  bizType?: number
}
export interface TranslateDocReqVO extends BasicTranslate {
  fileId: string
}

export interface TranslateDetectReqVO {
  content: string
  transType: number
}

export interface TranslatePageReqVO {
  pageNo: number
  pageSize: number
  status: string
  fileId: string
  msg: string
  originLanguage: string
  targetLanguage: string
  content: string // 翻译内容
  createTime: string // 创建时间
}
// 只包含 pageNo 和 pageSize
export type PageRequest = Pick<TranslatePageReqVO, 'pageNo' | 'pageSize'>
export interface PageResultAppTranslateRespVO {
  id: number
  status: number
  fileId: string
  originFileInfo: FileInfo
  msg: string
  originLang: number
  targetLang: number
  resultFileInfo: FileInfo
  createTime: string
  originContent?: string
  resultContent?: string
}
export interface CommonResultAppTranslateRespVO extends PageResultAppTranslateRespVO {}
export interface CommonResultPageResultAppTranslateRespVO {
  list: PageResultAppTranslateRespVO[]
  total: number
}
// Asr API
export const TranslateApi = {
  // 文本翻译
  translateWords: async (data: TranslateWordsReqVO) => {
    return await request.appPost({ url: `/ai/translate/words`, data })
  },
  // 文档翻译
  translateDoc: async (data: TranslateDocReqVO) => {
    return await request.appPost({ url: `/ai/translate/document`, data })
  },
  detectLanguage: async (data: TranslateDetectReqVO) => {
    return await request.appPost({ url: '/ai/translate/language-detection', data })
  },
  translateModelSimpleList: async (params) => {
    return await request.appGet({
      url: `/ai/translate/model/simple-list?locale=${params.lang}`,
      params
    })
  },
  getTranslationPage: async (params: TranslatePageReqVO | PageRequest) => {
    return await request.appGet<CommonResultPageResultAppTranslateRespVO>({
      url: '/ai/translate/page',
      params
    })
  },
  deleteHistoryById: async (id: number) => {
    return await request.appDelete({ url: `/ai/translate/delete?id=${id}` })
  },
  deleteAllHistory: async () => {
    return await request.appPost({ url: `/ai/translate/clean-history` })
  },
  getHistoryById: async (id: number) => {
    return await request.appGet({ url: `/ai/translate/get?id=${id}` })
  },
  getModelOptionList: async (bizType: number) => {
    return await request.appGet({ url: `/ai/translate/model/simple-list?bizType=` + bizType })
  }
}
