import request from '@/config/axios'

/**
 * 获取课程章节信息(要播放的内容信息)
 * @param courseId 课程ID
 * @returns 课程章节列表
 */
export const getPlay = async (id: number) => {
  return await request.get({
    url: `/learning/course-chapter/get?id=${id}`
  })
}

/**
 * 更新当前课程记录
 * @param data 传递课程章节ID，课程ID，结束时间，开始时间
 * @returns 课程记录结果
 */
export const updateRecord = async (data: any) => {
  return await request.post({
    url: '/learning/course/study/progress',
    data
  })
}

/**
 * 获取课程学习记录
 * @param courseId 课程ID
 * @param courseChapterId 课程章节ID
 * @returns 课程记录
 */
export const getRecord = async (courseId: number, courseChapterId: number) => {
  return await request.get({
    url: '/learning/course/study/chapter',
    params: {
      courseId,
      courseChapterId
    }
  })
}

/**
 * 查询课程学习进度
 * @param courseId 课程ID
 * @returns 学习进度
 */
export const getStudyInfo = async (courseId: any) => {
  return await request.get({
    url: '/adapter/course/study/info',
    params: {
      courseId
    }
  })
}
