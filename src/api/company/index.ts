import request from '@/config/axios'

/** 公司政策查询参数 */
export interface CompanyPolicyNavReqVO {
  /** 页码，从 1 开始 */
  pageNo: number
  /** 每页条数，最大值为 100 */
  pageSize: number
  /** 标题 */
  title?: string
  /** 状态（0：Not started，1：In progress，3：Completed） */
  status?: string
}

/** 公司政策响应对象 */
export interface AppCompanyPolicyPageRespVO {
  /** 主键ID */
  id: number
  /** 部门id */
  departmentId: number
  /** 部门名称 */
  departmentName: string
  /** 标题 */
  title: string
  /** 封面图 */
  cover: string
  /** 声明 */
  declaration: string
  /** 内容 */
  content: string
  /** 是否确认 */
  ack: boolean
  /** 排序码 */
  sort: number
  /** 文件类型列表 */
  fileTypeList: string[]
  /** 状态（0：Not started，1：In progress，3：Completed） */
  status: number
  /** 状态标签 */
  status_: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建人 */
  creator: number
  /** 更新人 */
  updater: number
}

/** 分页结果 */
export interface PageResultAppCompanyPolicyPageRespVO {
  /** 数据列表 */
  list: AppCompanyPolicyPageRespVO[]
  /** 总量 */
  total: number
}

/** 向后兼容的类型别名 */
export interface CompanyPolicyListVO extends AppCompanyPolicyPageRespVO {}

export interface CompanyPolicyUpdateVO {
  companyPolicyId: number
  status: number
}

/**
 * 导航 查询
 * @param params
 * @returns
 */
export const companyPolicyList = async (params: CompanyPolicyNavReqVO) => {
  return await request.get<CompanyPolicyListVO[]>({
    url: '/learning/company-policy/nav/page',
    params
  })
}
/**
 * My-todo 查询
 * @param params
 * @returns
 */
export const companyPolicyTodoList = async (params: CompanyPolicyNavReqVO) => {
  return await request.get({
    url: '/learning/company-policy/todo/page',
    params
  })
}
/**
 * 个人中心-分页查询公司政策
 * @param params 查询参数
 * @returns 分页结果
 */
export const companyPolicyCenterList = async (params: CompanyPolicyNavReqVO) => {
  return await request.appGet<PageResultAppCompanyPolicyPageRespVO>({
    url: '/learning/company-policy/nav/page',
    params
  })
}
/** Company Policy对应的详情页面 **/
export const getDetail = async (id: number) => {
  return await request.get({
    url: `/learning/company-policy/get?id=${id}`
  })
}
/** 修改公司政策状态 */
export const editStatus = async (data: CompanyPolicyUpdateVO) => {
  return await request.put({
    url: `/learning/company-policy/record/status`,
    data
  })
}
/** 查询状态 */
export const searchStatus = async (companyPolicyId: number) => {
  return await request.get({
    url: `/learning/company-policy/record/${companyPolicyId}`
  })
}
