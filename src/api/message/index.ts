import request from '@/config/axios'

export interface MessageReqVO {
  pageNo: number
  pageSize: number
  bizType: number
  readStatus: number
  createTime: string[]
}

export interface MessageRespVO {
  id: number
  userId: number
  userType: string
  templateId: number
  templateCode: string
  templateNickname: string
  templateContent: string
  templateType: number
  bizType: number
  bizNo: string
  readStatus: boolean
  readTime: Date
  createTime: Date
}

export interface MessageMyBizTypeRespVO {
  bizType: number // 业务类型
  count: number // 全部数量
  unreadCount: number // 未读数量
}

/**
 * 业务类型分类枚举
 */
export enum BizTypeEnum {
  CERTIFICATE_USER = 1,
  WAITING_LIST_PREFER_DATE = 2,
  CLASS_END = 3,
  CLASS_CANCEL = 4,
  CLASS_POSTPONE = 5,
  CLASS_PUBLISH = 6,
  LEANING_TASK_APPROVED = 7,
  LEANING_TASK_REJECTED = 8
}

/**
 * 消息是否已读枚举
 */
export enum ReadStatusEnum {
  NO = 0,
  YES = 1
}

// Live Stream API 直播
export const MessageCenterApi = {
  // 获得我的站内信分页(左侧分类)
  getMessageMyBizType: async () => {
    return await request.appGet<IResponse<MessageMyBizTypeRespVO[]>>({
      url: `/system/notify-message/my-bizTaype`
    })
  },

  // 获得我的站内信分页
  getMessagePage: async (params: MessageReqVO) => {
    return await request.appGet<PageResult<MessageRespVO[]>>({
      url: `/system/notify-message/my-page`,
      params
    })
  },

  // 获得站内信详情
  getMessage: async (id: number) => {
    return await request.appGet<IResponse<MessageRespVO>>({
      url: `/system/notify-message/get?id=` + id
    })
  },
  // 标记站内信为已读
  markMessageRead: async (ids: number[]) => {
    return await request.appPut({
      url: `/system/notify-message/update-read?ids=` + ids
    })
  }
}
