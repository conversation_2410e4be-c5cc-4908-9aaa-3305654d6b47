import request from '@/config/axios'

/** ----- ENUM ----- */
export enum TrainingNeedStatusEnum {
  DRAFT = 1, // 草稿
  SUBMITTED = 2, // 提交
  APPROVED = 3, // 审批通过
  REJECTED = 4, // 审批拒绝
  IGNORE = 5 // 忽略
}

export enum TrainingTypeEnum {
  MLC_TRAINING = 1, // 线下培训
  ONLINE_TRAINING = 2, // 线上培训
  ON_JOB_TRAINING = 3 // 在职培训
}

/** ----- INTERFACE ----- */
/** 培训需求分页请求参数 */
export interface TrainingNeedPageReqVO extends PageParam {
  title?: string // 标题（可选）
  status?: TrainingNeedStatusEnum
  trainingType?: TrainingTypeEnum // 培训类型
}

/** 培训内容类型 */
export interface TrainingContent {
  id: number // 内容ID
  applyId: number // 申请ID
  skillName: string // 技能名称
  trainingType: TrainingTypeEnum
  contentCatalog: string // 内容目录 (md格式)
  status: number
}

/** 培训申请项类型 */
export interface TrainingNeedRespVO {
  id: number // ID
  title: string // 标题
  content: TrainingContent[] // 培训内容数组
  status: TrainingNeedStatusEnum
  approveReason: string // 审批原因
}

/** 创建&更新培训内容请求数据类型 */
export interface TrainingContentSaveVO {
  id?: number // 培训资源内容ID（示例值：1683）
  applyId?: number // 申请ID（示例值：18099）
  skillName: string // 技能名称（示例值：数仓）
  contentType: TrainingTypeEnum // 培训方式：1.MLC Training、2.Content course、3.On-Job Training
  contentCatalog: string // 内容目录（markdown格式）
  status?: number // 状态（保留）
}

/** 创建&更新培训申请请求数据类型 */
export interface TrainingNeedSaveReqVO {
  id?: number // 培训申请ID（示例值：25179）
  title: string // 标题
  content: TrainingContentSaveVO[] // 培训内容数组
  status: number // 状态（1.草稿，2.提交）
}

/** 导出培训申请请求数据类型 */
export interface ExportTrainingNeedReqVO {
  pageNo: number // 页码，从 1 开始（必填）
  pageSize: number // 每页条数，最大值为 100（必填）
  title?: string // 标题（可选）
  status?: string // 状态（可选，具体类型可根据实际枚举设为 string 或 number）
  createTime?: string // 创建时间（可选，建议格式 YYYY-MM-DD 或时间戳）
}

/** ----- API ----- */
export const TrainingNeedAPI = {
  // 获得培训申请分页
  getTrainingNeedPage: (params: TrainingNeedPageReqVO) => {
    return request.appGet<PageResult<TrainingNeedRespVO[]>>({ url: 'edp/training-apply/page', params })
  },

  // 获取培训申请详情
  getTrainingNeedDetail: (id: number) => {
    return request.appGet({ url: `edp/training-apply/get?id=${id}` })
  },

  // 创建培训申请
  createTrainingNeed: (data: TrainingNeedSaveReqVO) => {
    return request.appPost({ url: 'edp/training-apply/create', data })
  },

  // 更新培训申请
  updateTrainingNeed: (data: TrainingNeedSaveReqVO) => {
    return request.appPut({ url: '/edp/training-apply/update', data })
  },

  // 删除培训申请
  deleteTrainingNeed: (id: number) => {
    return request.appDelete({ url: `edp/training-apply/delete?id=${id}` })
  },

  // 导出培训申请 Excel
  exportTrainingNeed: (params: ExportTrainingNeedReqVO) => {
    return request.appDownload({
      url: 'edp/training-apply/export-excel',
      params,
      responseType: 'blob'
    })
  }
}