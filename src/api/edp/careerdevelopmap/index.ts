import request from '@/config/axios'

/** ----- INTERFACE ----- */
/** 岗位路线图响应数据格式 */
export interface CareerDevelopMapRespVO {
  id: number // ID
  positionId: number // 岗位ID
  positionName: string // 岗位名称
  sectName: string // 子部门名称
  parentId: number // 上级节点ID
  root: boolean // 是否为根节点：0/1 => false/true
  children: CareerDevelopMapRespVO[] // 子集（功能上需递归）
}

/** ----- API ----- */
export const CareerDevelopMapAPI = {
  // 获取岗位路线图
  getCareerDevelopMap: () => {
    return request.appGet({ url: 'edp/career-develop/list' })
  }
}