import request from '@/config/axios'

/** ----- ENUM ----- */
/** 定义阶段枚举值 */
export enum PhaseEnum {
  PHASE1 = 1, // 阶段Ⅰ
  PHASE2 = 2, // 阶段Ⅱ
  PHASE3 = 3 // 阶段Ⅲ
}

/** 定义学习内容类型枚举 */
export enum StudyContentTypeEnum {
  COURSE = 10, // 在线课程
  TRAINING = 50, // 线下培训
  KNOWLEDGE = 60 // 知识库
}

/** ----- INTERFACE ----- */
/** 获取阶段统计请求数据类型 */
export interface PhaseStatisticReqVO {
  phase: number // 阶段（必填）
  positionId: number // 岗位ID（必填）
}

/** 学习计划列表数据类型 */
export interface StudyPlanReqVO {
  phase: number // 难度等级（必填）
  pageNo: number // 页码（必填，从1开始）
  pageSize: number // 每页条数（必填，最大100）
  positionId?: number // 岗位ID（选填）
  skillId?: number // 技能ID（选填）
  bizType?: number // 学习内容类型（选填）
  title?: string // 内容标题（选填）
  startDate?: string // 开始日期（选填）
  endDate?: string // 结束日期（选填）
}

/** 学习计划内容类型 */
export interface StudyPlanContent {
  planContentId: number
  recommendId: number
  bizType: number
  title: string
  skillId: number
  planId: number
  status: number
  progress: number
  skipped: boolean
  skipReason: string
}

/** 学习计划响应数据类型 */
export interface StudyPlanRespVO {
  id: number
  positionId: number
  skillId: number
  skillName: string
  firstLevelSkillName: string
  secondLevelSkillName: string
  progress: string
  status: number
  startDate: string // 格式为 MM-DD-YYYY
  endDate: string // 格式为 MM-DD-YYYY
  studyPlanContentList: StudyPlanContent[]
}

/** 请求学习计划分页数据类型 */
export interface StudyPlanPageRespVO {
  list: StudyPlanRespVO[]
  total: number
}

/** 学习计划内容响应数据类型 */
export interface PlanContentRespVO {
  planContentId: number // 计划内容ID
  recommendId: number // 学习内容推荐ID
  bizType: number // 内容类型（例如：1）
  title: string // 内容名称（如：无字天书）
  skillId: number // 技能ID
  planId: number // 学习计划ID
  status?: number // 状态：1正常 2新增 3删除（可选）
  progress?: number // 学习进度（百分比）（可选）
  skipped?: boolean // 是否跳过（可选）
  skipReason?: string // 跳过原因（可选）
}

/** 创建学习计划请求数据类型 */
export interface CreateStudyPlanReqVO {
  id: number // 计划ID
  positionId: number // 岗位ID
  skillId: number // 技能ID
  startDate: string // 开始日期（格式：YYYY-MM-DD）
  endDate: string // 结束日期（格式：YYYY-MM-DD）
  studyPlanContentList?: PlanContentRespVO[] // 学习计划内容列表（可选）
}

/** 跳过学习计划请求数据类型 */
export interface SkipStudyPlanReqVO {
  id: number // 学习计划ID
  contentIds: string // 学习计划内容ID列表
  reason: string // 跳过原因
}

/** ----- API ----- */
export const StudyPlanApi = {
  // 获取阶段统计
  getPhaseStatistics: async (params: PhaseStatisticReqVO) => {
    return request.appGet({ url: `edp/study-plan/phase-statistic`, params })
  },

  // 查询学习计划分页
  getStudyPlanPage: async (params: StudyPlanReqVO) => {
    return request.appGet({ url: `edp/study-plan/page`, params })
  },

  // 创建学习计划
  createStudyPlan: async (data: CreateStudyPlanReqVO) => {
    return request.appPost({ url: `edp/study-plan/create`, data })
  },

  // 更新学习计划
  updateStudyPlan: async (data: CreateStudyPlanReqVO) => {
    return request.appPut({ url: `edp/study-plan/update`, data })
  },

  // 跳过学习计划内容
  skipStudyPlan: async (params: SkipStudyPlanReqVO) => {
    return request.appPut({ url: `edp/study-plan/skip`, params })
  },

  // 移除学习计划
  removeStudyPlan: async (id: number) => {
    return request.appDelete({ url: `edp/study-plan/delete/${id}` })
  }
}