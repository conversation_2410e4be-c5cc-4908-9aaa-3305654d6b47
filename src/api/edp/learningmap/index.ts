import request from '@/config/axios'

/** ----- ENUM ----- */
/** 定义阶段枚举值 */
export enum PhaseEnum {
  PHASE1 = 1, // 阶段Ⅰ
  PHASE2 = 2, // 阶段Ⅱ
  PHASE3 = 3 // 阶段Ⅲ
}

/** 定义一级技能枚举值 */
export enum FirstLevelSkillEnum {
  SOFT_SKILL = 1, // 软技能
  HARD_SKILL = 2, // 硬技能
  HSE = 3, // HSE
}

/** ----- INTERFACE ----- */
export interface LearningMap {
  contentTitles: string[]
  progress: number
  skillContentNumber: Record<number, number>
}

export type PhaseLearningMap = Record<PhaseEnum, LearningMap>

export const LearningMapApi = {
  // 通过岗位ID获取岗位学习地图
  getLearningMapByPositionId: async (positionId: number) => {
    return request.appGet<PhaseLearningMap>({
      url: `edp/position-learning-map/get?positionId=${positionId}`
    })
  }
}