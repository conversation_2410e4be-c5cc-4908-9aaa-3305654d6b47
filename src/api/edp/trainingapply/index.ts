import request from '@/config/axios'

// 培训申请 VO
export interface TrainingApplyVO {
  id: number // ID
  title: string // 标题
  deptId: number // 部门ID
  status: number // 状态（1.草稿，2.提交，3.审批通过，4.审批拒绝，5.不予理睬）
  approver: number // 审批人ID
  approveReason: string // 审批原因
}

// 培训申请 API
export const TrainingApplyApi = {
  // 查询培训申请分页
  getTrainingApplyPage: async (params: any) => {
    return await request.get({ url: `/edp/training-apply/page`, params })
  },

  // 查询培训申请详情
  getTrainingApply: async (id: number) => {
    return await request.get({ url: `/edp/training-apply/get?id=` + id })
  },

  // 新增培训申请
  createTrainingApply: async (data: TrainingApplyVO) => {
    return await request.post({ url: `/edp/training-apply/create`, data })
  },

  // 修改培训申请
  updateTrainingApply: async (data: TrainingApplyVO) => {
    return await request.put({ url: `/edp/training-apply/update`, data })
  },

  // 删除培训申请
  deleteTrainingApply: async (id: number) => {
    return await request.delete({ url: `/edp/training-apply/delete?id=` + id })
  },

  // 导出培训申请 Excel
  exportTrainingApply: async (params) => {
    return await request.download({ url: `/edp/training-apply/export-excel`, params })
  }
}