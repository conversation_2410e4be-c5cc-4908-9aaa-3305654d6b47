import request from '@/config/axios'

export interface ConfigOptions {
  optionId: number // 配置id
  label: string // 展示label，请求的时候不用传
  value: boolean | string // 配置值
  name: string
  remark?: string
}
export interface RoomCreateOrUpdateOrPublishReqVO {
  cover?: string
  name?: string
  description?: string
  startTime?: string
  endTime?: string
  privacy?: number
  speakerIds?: number[] // 主讲人id
  participantIds?: number[] // 参与人id列表
  attachmentIds?: number[] // 附件id列表
  configOptions?: ConfigOptions[] // 配置信息列表
  speakers?: RoomUserVO[]
  id?: string
}
export interface CreatedLiveReqVO {
  pageNo: number
  pageSize: number
  name: string
  statusList: LiveStatusEnum[]
  startTime: string[]
  startTimeFuzzy?: string
  sortRule?: number
}
export interface JoinedLiveReqVO {
  pageNo: number
  pageSize: number
  name: string
  status: number
  startTime: string[]
}
export interface RoomUserVO {
  userId: number
  nickname: string
  joinType: number
  role: number
  deptName: string
  deptId: number
  avatar: string
}
export interface RoomAttachmentVO {
  fileId: string
  fileName: string
  url: string
  type: number // 文件类型
  size: number // 文件大小
}
export interface RecordingVO {
  id: string
  roomId: string
  meetingId: string
  recordingId: string
  size: number
  duration: number
  url: string
  visible: number
  name: string
}
export interface RoomDetailVO {
  id: string
  cover: string
  description: string
  name: string
  privacy: number // 公开还是私密 私密性 1.公开 2.私密
  startTime: string
  endTime: string
  status: LiveStatusEnum // 直播间状态 10.待发布 20.待直播 50.直播中 90.已结束
  speakers: RoomUserVO[]
  invitedUsers: RoomUserVO[]
  roomAttachments: RoomAttachmentVO[]
  roomConfigOptions: ConfigOptions[] //配置信息列表
  roomUsers: RoomUserVO[]
  liveLinkUrl: string
  userId: number
  reserved: boolean
  recordings: RecordingVO[]
  favourite: boolean
}
export interface RoomListVO {
  id: string
  cover: string
  name: string
  userId: number
  nickname: string
  status: number
  privacy: number
  startTime: string
  createTime: string
  speakers: RoomUserVO[]
  reservationTotal: number // 预约人数
  reserved: boolean // 是否预约
  joinType: LiveJoinTypeEnum // 入会方式
  liveLinkUrl: string // 直播链接
  favourite: boolean
  isShowCountDown?: boolean
  countDownTextColor?: number
  remainingTime?: number
}
export interface PageResultRoomListVO {
  list: RoomListVO[]
  total: number
}
export interface DeptAndUserMixedVO {
  type: number // 1.部门 2.用户
  id: number // 混合信息对应id
  name: string // 混合信息名称
  avatar: string
}

/**
 * 直播状态枚举
 * 10.待发布 20.待直播 50.直播中 90.已结束
 */
export enum LiveStatusEnum {
  PENDING = 10,
  UPCOMING = 20,
  LIVING = 50,
  OVER = 90
}

export enum LiveJoinTypeEnum {
  RESERVE = 10,
  INVITE = 20
}

/**
 * 直播列表排序枚举
 * 1.按开始时间排序 2.按预约人数排序
 */
export enum LiveSortEnum {
  NEWEST = 1,
  HOTTEST = 2
}

/**
 * 直播操作类型枚举
 * 1.加入 2.预约 3.取消预约 4.取消加入
 */
export enum LiveOperationTypeEnum {
  SUBSCRIBE = 1,
  PUBLISH = 2,
  JOIN3 = 3,
  JOIN4 = 4,
  UNSUBSCRIBE = 5
}

// Asr API
export const LiveApi = {
  // 创建转写
  createRoom: async (data: RoomCreateOrUpdateOrPublishReqVO) => {
    return await request.appPost({ url: `/live/room/create`, data })
  },
  getCreatedRoomPage: async (params: CreatedLiveReqVO) => {
    return await request.appGet<PageResultRoomListVO>({ url: `/live/room/created-page`, params })
  },
  getRecordingPage: async (params: CreatedLiveReqVO) => {
    return await request.appGet<PageResultRoomListVO>({ url: `/live/room/recording-page`, params })
  },
  getJoinedRoomPage: async (params: CreatedLiveReqVO) => {
    return await request.appGet<PageResultRoomListVO>({
      url: `/live/room/participated-page`,
      params
    })
  },
  getRoomBrief: async (id: string) => {
    return await request.appGet<RoomDetailVO>({ url: `/live/room/get-brief?id=` + id })
  },
  getRoomDetail: async (id: string) => {
    return await request.appGet<RoomDetailVO>({ url: `/live/room/get-detail?id=` + id })
  },
  joinRoom: async (id: string) => {
    return await request.appGet({ url: `/live/room/join?id=` + id })
  },
  // 创建转写
  publishRoom: async (data: RoomCreateOrUpdateOrPublishReqVO) => {
    return await request.appPost({ url: `/live/room/publish`, data })
  },
  // 创建转写
  publishRoomDirect: async (id: string) => {
    return await request.appPost({ url: `/live/room/publish-direct?id=${id}` })
  },
  // 编辑直播房间信息
  // 创建转写
  updateRoom: async (data: RoomCreateOrUpdateOrPublishReqVO) => {
    return await request.appPost({ url: `/live/room/update`, data })
  },
  getVisibleRoom: async (params: CreatedLiveReqVO) => {
    return await request.appGet<PageResultRoomListVO>({ url: `/live/room/visible-page`, params })
  },
  getMixedDeptUserList: async (deptId: number) => {
    return await request.appGet<DeptAndUserMixedVO[]>({
      url: `/system/mixed/dept-user-list?deptId=${deptId}`
    })
  },
  getConfigList: async () => {
    return await request.appGet<ConfigOptions[]>({
      url: `/live/config/room-config/simple-list`
    })
  },
  subscribeLive: async (roomId: string) => {
    return await request.appPost({ url: `/live/room/reserve?roomId=${roomId}` })
  },
  unSubscribeLive: async (roomId: string) => {
    return await request.appPost({ url: `/live/room/cancel-reserve?roomId=${roomId}` })
  },
  deleteRoomById: async (id: string) => {
    return await request.appDelete({ url: `/live/room/delete?id=${id}` })
  },
  // 收藏直播
  addFavorite: async (id: string) => {
    return await request.appPut({ url: `/live/room/add-favorite?id=${id}` })
  },
  // 移除收藏
  removeFavorite: async (id: string) => {
    return await request.appPut({ url: `/live/room/remove-favorite?id=${id}` })
  },
  // 收藏列表
  getFavoriteList: async (params: CreatedLiveReqVO) => {
    return await request.appGet<PageResultRoomListVO>({
      url: `/live/room/favorite-page`,
      params
    })
  },
  // 更新录播的可见信息
  updateRecordingVisible: async (id: string, visible: number) => {
    return await request.appPut({
      url: `/live/room-recording/update-recording-visible?id=${id}&visible=${visible}`
    })
  }
}
