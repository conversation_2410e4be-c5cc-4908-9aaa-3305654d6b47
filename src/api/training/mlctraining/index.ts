import request from '@/config/axios'

export interface TrainingReqVO {
  pageNo: number
  pageSize: number
  title: string
  code: string
}
export interface TrainingRespVO {
  id: number
  title: string
  code: string
  categoryId: number
  categoryFullPath: string
  examId: number
  cover: string
  language: number
  languageList: string[]
  languageStr: string[]
  trainerType: number
  approval: number
  scope: number
  validity: number
  feedbackStatus: number
  feedbackLanguage: number
  absentTime: number
  freezeTime: number
  certificateId: number
  notification: number
  bookingTime: number
  remarks: string
  createTime: string
}

/**
 * 筛选时间枚举
 */
export enum TimeTypeEnum {
  THIS_WEEK = 1,
  THIS_MONTH = 2,
  THIS_YEAR = 3
}

/**
 * 预定类型枚举
 */
export enum BookingTypeEnum {
  BOOKING_FOR_MY_SELF = 1,
  BOOKING_FOR_MY_OTHER = 0
}

/**
 * 课堂发布状态枚举 状态(0.未发布 1.发布)
 */
export enum PublishStatusEnum {
  PUBLISHED = 1,
  UNPUBLISHED = 0
}

/**
 * 课堂查询类型枚举 查询类型(1. 我的记录 2.通过课程id查询)
 */
export enum QueryTypeEnum {
  MY_SELF = 1,
  COURSE_ID = 2
}

// TrainingApi API 课程
export const TrainingApi = {
  // 获得学院课程信息分页
  getTrainingPage: async (params: TrainingReqVO) => {
    return await request.appGet<PageResult<TrainingRespVO[]>>({
      url: `/academy/course/page`,
      params
    })
  },
  // 获得学院课程信息
  getTraining: async (id: number) => {
    return await request.appGet<IResponse<TrainingRespVO>>({
      url: `/academy/course/get?id=` + id
    })
  }
}
