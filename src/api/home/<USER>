import request from '@/config/axios'

export interface TodoCoursesReqVO extends PageParam {
  userId?: number;             // 用户 ID
  name?: string;               // 课程名称
  topicId?: number;            // 专题 ID
  parentTopicId?: number;      // 父级专题 ID
  subTopicIdList?: number[];   // 子级专题 ID 数组
  type?: number;                // 类型（0：选修，1：必修）
  courseType?: number;          // 新课/热课类型（0：新课，1：热课）
  levelList?: number[]; // 课程级别（0：所有，1：初级，2：中级，3：高级）
  languageList?: number[];     // 语言（如 1：英语，2：阿拉伯语）
  subtitleList?: number[];     // 字幕（1：有字幕，0：无字幕）
  sourceList?: number[];       // 课程来源（1：本地，2：云端）
  durationTypeList?: number[]; // 时长类型（1:<15min, 2:15-30min, 3:30-60min, 4:>60min）
}

export interface CourseAssignScopeVO {
  id: number;
  relevanceId: number;
  relevanceName: string;
  scope: number; // 范围
  type: number;             // 类型（0：公开，1：必修）
  topic: string;
  deadline?: string;      // 截止时间（ISO 日期字符串）
}

export interface TodoCoursesVO {
  createTime: string;                   // 创建时间（ISO 日期字符串）
  updateTime: string;                   // 更新时间（ISO 日期字符串）
  creator: string;
  updater: string;
  deleted: boolean;
  id: number;                           // 主键 ID
  topicId: string;                      // 专题（多个用逗号分隔的字符串）
  parentTopicId: number;
  name: string;
  cover: string;
  keywords: string;
  lang: string;
  duration: number;                     // 预估时长（秒）
  durationLower: number;
  durationUpper: number;
  introduction: string;
  deptId: number;
  status: number;                        // 状态（0：下架，1：上架）
  shelfTime: string;                    // 上架时间（ISO 日期字符串）
  effectiveDay: number;                 // 课程时效（-1 表示永久）
  isAutoAssign: boolean;
  isCertificateGenerated: boolean;
  isRecommend: boolean;
  isNew: boolean;
  level: number;                 // 课程等级
  enrollNumber: number;
  pageView: number;
  star: number;                         // 总评分
  scopes: CourseAssignScopeVO[];       // 课程分配信息
  myStar: number;                       // 我的评分
  electiveNum: number;
  requiredNum: number;
  requiredAndCompletedNum: number;
  requiredAndNotCompletedNum: number;
  studyStatus: number;               // 学习状态
  chapterNum: number;
  examNum: number;
  assigned: boolean;
  certificateUrl: string;
  topicIdList: number[];
  subTopicIdList: number[];
  language: number;                     // 语言 (1.英语 2.阿拉伯语)
  subtitle: boolean;                    // 是否有字幕
  source: number;                        // 来源（1：本地，2：云端）
  handDuration: number;
  handDurationLower: number;
  handDurationUpper: number;
  contentId: string;
  assetUuid: string;
  exam: number;                          // 是否有考试
  courseIds: number[];
}

export const getTodoCourses = async (params: TodoCoursesReqVO) => {
  return await request.appGet({
    url: '/learning/course/todoList',
    params
  })
}

export const listCaursoul = async (params: any) => {
  return await request.get({
    url: 'http://10.248.18.22:48080/admin-api/system/banner',
    params
  })
}

export const getStatistics = async () => {
  return await request.appGet({
    url: '/learning/home/<USER>'
  })
}

export const getNews = async (id: number) => {
  return await request.get({
    url: `http://10.248.18.22:48080/admin-api/system/banner/${id}`
  })
}

export const getMyTaskNum = async () => {
  return await request.appGet({
    url: '/learning/home/<USER>'
  })
}