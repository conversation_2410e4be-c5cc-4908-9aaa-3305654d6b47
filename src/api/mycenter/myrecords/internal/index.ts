import request from '@/config/axios'

export interface InternalTrainingReqVO {
  pageNo: number
  pageSize: number
  implementingCompany: string // 培训公司
  startTime: string
  endTime: string
  title: string
  titleAr: string
  createTime: Date
}
export interface InternalTrainingRespVO {
  id?: number
  code: string
  title: string
  titleAr: string
  type: number
  place: string
  startTime: Date
  endTime: Date
  duration: number
  implementingCompany: string
  attachments: string
  remark: string
  createTime: Date
  userCount: number
}

/**
 * 国内课程类型枚举
 */
export enum InternalTypeEnum {
  COURSE = 1,
  TRAINING = 2,
  VISIT = 3,
  CONFERENCE = 4,
  LECTURE = 5,
  MEETING = 6,
  WORKSHOP = 7
}

// Internal TrainingApi API 课堂
export const InternalTrainingApi = {
  // 获得国内培训分页
  getInternalTrainingPage: async (params: InternalTrainingReqVO) => {
    return await request.appGet<PageResult<InternalTrainingRespVO[]>>({
      url: `/academy/internal/page`,
      params
    })
  }
}
