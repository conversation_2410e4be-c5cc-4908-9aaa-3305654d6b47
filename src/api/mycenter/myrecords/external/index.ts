import request from '@/config/axios'

export interface ExternalTrainingReqVO {
  pageNo: number
  pageSize: number
  costBearer: string // 费用承担公司
  receivingCountry: string // 海外培训地点
  travelDate: string
  returnDate: string
  title: string
  titleAr: string
  createTime: Date
}
export interface ExternalTrainingRespVO {
  id?: number
  code: string
  title: string
  titleAr: string
  receivingCountry: string
  travelDate: Date
  returnDate: Date
  adminNo: string
  costBearer: string
  attachments: string
  remark: string
  createTime: Date
  userCount: number
}

// External TrainingApi API 课堂
export const ExternalTrainingReqTrainingApi = {
  // 获得海外培训分页
  getExternalTrainingPage: async (params: ExternalTrainingReqVO) => {
    return await request.appGet<PageResult<ExternalTrainingRespVO[]>>({
      url: `/academy/external/page`,
      params
    })
  }
}
