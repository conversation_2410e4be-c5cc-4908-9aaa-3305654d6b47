import request from '@/config/axios'

export interface MlcTrainingReqVO {
  pageNo: number
  pageSize: number
  courseName: string
  trainerId: number
  classRoomId: number
  studyStatusList: number[]
  type: number
  startDate: string
  endDate: string
  queryType: number
}
export interface MlcTrainingRespVO {
  id: number
  classId: number
  courseId: Number
  courseName: string
  categoryName: string
  name: string
  code: string
  type: number
  trainerId: number
  classRoomId: number
  trainerName: string
  classRoomName: string
  scanStartTime: string
  startDate: string
  trainingDays: number
  language: number
  translator: number
  studyStatus: number
  status?: number
  publishStatus: number
  maxNum: number
  minNum: number
  liveLink: string
  description: string
  createTime: string
  assignNum: number
  classInfo: {
    classId: number
    courseCover: string
  }
  startTime: string
  endTime: string
  validity: string | number
  classStatus?: number
}

export interface ClassBookingRespVO {
  id: number
  processInstanceId: number
  classId: number
  classInfo: {
    id: number
    courseId: number
    courseName: string
    courseTrainerType: number
    name: string
    examId: number
    code: string
    type: number
    trainerId: number
    trainerName: string
    classRoomId: number
    classRoomName: string
    scanStartTime: Date
    startDate: string
    startTime: string
    endTime: string
    trainingDays: number
    language: number
    translator: number
    status: number
    studyStatus: number
    publishStatus: number
    bookingStartTime: Date
    bookingEndTime: Date
    maxNum: number
    minNum: number
    liveLink: string
    liveId: number
    feedbackQrCode: string
    description: string
    createTime: Date
    assignNum: number
    attendRate: number
    trainerScore: number
    assignmentType: number
  }
  userId: number
  type: number
  courseJson: string
  attachmentJson: string
  status: number
  ddtPermitNo: string
  dateOfBirth: string
  drivingLicenceNumber: string
  issuingDate: Date
  expiryDate: Date
  vehicle: string
  eyeTest: string
  createTime: Date
  firstStudy: boolean
  approvedTotal: number
  total: number
}
export interface ClassReqVO {
  pageNo: number
  pageSize: number
  courseId: number
  createTime: []
  queryType: number
  classId: number
  userIds: number[]
}

export interface WaitingListSaveVO {
  id: number
  courseId: number
  classId: number
  language: number
  translator: number
  userIds: number[]
  type: number
  startPreferredDate: string
  endPreferredDate: string
}

export interface DeptAndUserMixedVO {
  type: number // 1.部门 2.用户
  id: number // 混合信息对应id
  name: string // 混合信息名称
  avatar: string
}

export interface ClassBookingSaveVO {
  id: number
  courseId: number
  classId: number
  userId: number[]
  type: number
  courseJson: number[]
  attachmentJson: number[]
  ddtPermitNo: string
  dateOfBirth: string
  drivingLicenceNumber: string
  issuingDate: string
  expiryDate: string
  vehicle: string
  eyeTest: string
}

export interface ClassFeedbackSaveVO {
  id: number
  classId: number
  courseEvaluation: number
  trainerEvaluation: number
  facilityEvaluation: number
  comments: string
}

/**
 * 检查课程是否学习完成
 */

export interface CheckCourseCompleteRespVO {
  courseName: string
  existStudy: boolean
}

/**
 * 课堂学习状态枚举
 */
export enum TrainingStudyStatusEnum {
  BOOKED = 1,
  PASSED = 2,
  FAIL = 3,
  POSTPONE = 4,
  REJECTED = 5
}

/**
 * 课堂预定状态枚举 预定状态（1.approving 2.rejected 3.approved 4.异常 5.取消）
 */
export enum ClassBookingStatusEnum {
  APPROVING = 1,
  Rejected = 2,
  APPROVED = 3,
  ABNORMAL = 4,
  CANCELLED = 5
}

/**
 * 课堂类型枚举
 */
export enum ClassTypeEnum {
  OFFLINE_CLASS = 1,
  VIRTUAL_CLASS = 2,
  HYBRID_CLASS = 3
}

/**
 * 课堂语言枚举
 */
export enum ClassLanguageEnum {
  EN = 1,
  AR = 2,
  CN = 3
}

/**
 * 课堂正在进行中状态枚举
 */
export enum ClassInprogressStatusEnum {
  BOOKED = 1,
  STUDYING = 2
}

/**
 * 课堂状态枚举
 */
export enum ClassStatusEnum {
  DRAFT = 0,
  NOT_STARTED = 1,
  ONGOING = 2,
  ENDED = 3,
  POSTPONED = 4,
  CANCELLED = 5
}

/**
 * 课程语言枚举
 */
export enum TrainingLanguageEnum {
  No = 0,
  EN = 1,
  AR = 2,
  CN = 3
}

/**
 * 申请类型枚举
 */
export enum WaitingTypeEnum {
  ADD_MYSELF = 1,
  ADD_OTHER = 2
}

/**
 * 是否可以预定枚举
 */
export enum ExistBookingEnum {
  YES = 1,
  NO = 0
}

// MLC TrainingApi API 课堂
export const MlcTrainingApi = {
  // 获得课堂信息分页---my records
  getTrainingPage: async (params: MlcTrainingReqVO) => {
    return await request.appGet<PageResult<MlcTrainingRespVO[]>>({
      url: `/academy/class-info/my-training`,
      params
    })
  },
  getTraining: async (id: number) => {
    return await request.appGet<IResponse<MlcTrainingRespVO>>({
      url: `/academy/class-info/get?id=` + id
    })
  },
  // 获得课堂详情信息
  getClass: async (id: number) => {
    return await request.appGet<IResponse<MlcTrainingRespVO>>({
      url: `/academy/class-info/get?id=` + id
    })
  },
  // 获得课堂预定详情分页---MyTodo ->左侧tab页签
  getClassBookingPage: async (params: ClassReqVO) => {
    return await request.appGet<PageResult<ClassBookingRespVO[]>>({
      url: `/academy/class-booking/page`,
      params
    })
  },
  // 获得课堂信息分页---MyTodo ->右侧tab页签
  getTodoTrainingPage: async (params: MlcTrainingReqVO) => {
    return await request.appGet<PageResult<MlcTrainingRespVO[]>>({
      url: `/academy/class-info/to-do`,
      params
    })
  },
  // 用户取消课堂
  cancelClass: async (classId: number) => {
    return await request.appPut({
      url: `/academy/class-info/user/cancel?classId=` + classId
    })
  },
  // 检查课程是否学习完成
  checkCourseComplete: async (courseIds: number[]) => {
    return await request.appGet<CheckCourseCompleteRespVO[]>({
      url: `/academy/class-info/check-class-study?courseIds=` + courseIds
    })
  },
  // 检查是否可以预定
  checkClassBooking: async (classId: number) => {
    return await request.appGet({
      url: `/academy/class-info/check-class-booking?classId=` + classId
    })
  },
  // 判断是否是供应商的负责人
  checkSupplierHolder: async (params: { companyId: number; userId: number }) => {
    return await request.appGet({
      url: `/system/user/supplierHolder`,
      params
    })
  },
  // 获得课堂下的人员
  getClassUserPage: async (params: { pageNo: number; pageSize: number; classId: number }) => {
    return await request.appGet({
      url: `/academy/waiting-list/page`,
      params
    })
  },
  // 创建课堂等待列
  createWaitingList: async (data: WaitingListSaveVO) => {
    return await request.appPost({
      url: `/academy/waiting-list/create`,
      data
    })
  },
  // 供应商部门和用户混合信息列表
  getMixedDeptUserList: async (deptId: number) => {
    return await request.appGet({
      url: `/system/mixed/contract-dept-user-list?deptId=` + deptId
    })
  },
  // 是否第一次学习
  isFirstStudy: async (params: { classId: number; userId: number }) => {
    return await request.appGet({
      url: `/academy/class-info/check-first-study`,
      params
    })
  },
  // 创建课堂预定详情
  createClassBooking: async (data: ClassBookingSaveVO) => {
    return await request.appPost({
      url: `/academy/class-booking/create`,
      data
    })
  },
  // 创建课堂反馈
  createClassFeedback: async (data: ClassFeedbackSaveVO) => {
    return await request.appPost({
      url: `/academy/class-feedback/create`,
      data
    })
  },
  // 获得课堂反馈详情信息
  getClassFeedback: async (id: number) => {
    return await request.appGet({
      url: `/academy/class-feedback/get?id=` + id
    })
  },
  // 加入直播
  joinLive: async (data: { classId: number }) => {
    return await request.post({
      url: `/academy/class-info/join/live`,
      data
    })
  },
  // 校验该课程是否已分配
  checkClassAssigned: async (classId: number) => {
    return await request.appGet({
      url: `/academy/class-info/check-class-assign?classId=` + classId
    })
  }
}
