import request from '@/config/axios'

// 查询考试列表
export const listExam = async (query: any) => {
  return await request.get({
    url: '/learning/exam/my',
    params: query
  })
}

// 根据id查询考试
export const getExam = async (examId: any) => {
  return await request.get({
    url: `/learning/exam/get?id=${examId}`
  })
}

// 查询考试答题结果
export const getExamResult = async (query: any) => {
  return await request.get({
    url: '/learning/exam/myExamResult',
    params: query
  })
}

// 开始考试
export const startExam = async (data: any) => {
  return await request.post({
    url: '/learning/exam/startExam',
    params: data
  })
}

// 开始课程考试
export const startCourseExam = async (data: any) => {
  return await request.post({
    url: '/learning/exam/startCourseExam',
    params: data
  })
}

// 提交考试
export const submitExam = async (query: any, data: any) => {
  return await request.post({
    url: '/learning/exam/submit',
    params: query,
    data
  })
}
