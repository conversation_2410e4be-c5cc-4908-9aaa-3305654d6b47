<script setup lang="ts">
import { h, computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import MostPopular from './Tabtodo/components/MostPopular/index.vue'
import { getMyTaskNum, getStatistics, listCaursoul, TodoCoursesVO } from '@/api/home'
import { secondsToHHmmss } from '@/utils/date'
import { useUserStore } from '@/store/modules/user'
import Autoplay from 'embla-carousel-autoplay'
import { ILinkItem } from '@/components/LinkNav/src/types'
import { UserStatus } from '@/utils/authControl'
import Exam from '@/views/exam/index.vue'
import Onboarding from './Tabtodo/components/todoonboard/index.vue'
import CompanyPolicy from './Tabtodo/components/todocompany/index.vue'
import ElectiveCourses from './Tabtodo/components/Elective/index.vue'
import Journey from './Tabtodo/components/Journey/index.vue'
import MandatoryCourses from './Tabtodo/components/todocourse/index.vue'
import Training from '@/views/home/<USER>/components/todotraining/index.vue'
import { getTodoCourses } from '@/api/home'
import {
  BookOpen,
  FileText,
  Users,
  Shield,
  GraduationCap,
  Map,
  Briefcase,
  ListTodo,
  LucideIcon,
  Plus,
  TrendingUp,
  Clock,
  Search,
  User,
  CheckCircle,
  AlertCircle,
  RotateCcw,
  ChevronRight,
  Award
} from 'lucide-vue-next'

const plugin = Autoplay({
  delay: 2000,
  stopOnMouseEnter: true,
  stopOnInteraction: false
})
const carouselItems = ref()
const loading = ref(false)
const router = useRouter()
const userStore = useUserStore()
const totalBadge = ref(0)
const totalPendingCourse = ref(0)
const totalPendingExam = ref(0)
const statisticsInfo = ref<{
  learningDuration: number
  totalCourseCount: number
  totalExamCount: number
}>({
  learningDuration: 0,
  totalCourseCount: 0,
  totalExamCount: 0
})
// 定义 TO DO List 分次请求
const mandatoryCourses = ref<TodoCoursesVO[]>([])
const electiveCourses = ref<TodoCoursesVO[]>([])
const exams = ref([])
const companyPolicies = ref([])
const learningJourney = ref([])
const liveStream = ref([])
const surveys = ref([])
const certifications = ref([])

interface TaskNumber {
  requiredCourseNum: number
  electiveNum: number
  examNum: number
  onboardingNum: number
  companyPolicyNum: number
  journeyNum: number
  total: number
}

const emit = defineEmits(['getTotalBadge', 'getTotalCourse', 'getTotalExam'])
const myTaskNum = ref<TaskNumber>()
const newtabButton = ref([])

interface Todo extends ILinkItem {
  component: string
  badge: number
  icon: LucideIcon
  color: string
  userStatus: UserStatus[]
}

const componentsMap = {
  MandatoryCourses: MandatoryCourses,
  Exam: Exam,
  Onboarding: Onboarding,
  CompanyPolicy: CompanyPolicy,
  Journey: Journey,
  ElectiveCourses: ElectiveCourses,
  Training: Training,
  Survey: () =>
    h('div', { class: 'p-4 text-center text-muted-foreground' }, 'Survey component coming soon...'),
  NewCourse: () => h(MostPopular, { courseType: 0 }),
  MostPopular: () => h(MostPopular, { courseType: 1 })
}

const todoList = reactive<Todo[]>([])

// 课程推荐 tabs
const courseRecommendationTabs = reactive([
  {
    name: 'New Course',
    component: 'NewCourse',
    icon: Plus,
    color: 'orange',
    badge: 0,
    active: false,
    courseType: 0
  },
  {
    name: 'Most Popular',
    component: 'MostPopular',
    icon: TrendingUp,
    color: 'amber',
    badge: 0,
    active: false,
    courseType: 1
  }
])

// 合并所有 tabs
const allTabs = computed(() => [...todoList, ...courseRecommendationTabs])
const activeTodo = ref<Todo>(todoList[0])
const getTaskNumber = () => {
  getMyTaskNum().then((res) => {
    myTaskNum.value = res
    if (myTaskNum.value) {
      todoList[0].badge = myTaskNum.value.requiredCourseNum
      todoList[1].badge = myTaskNum.value.examNum
      todoList[2].badge = myTaskNum.value.onboardingNum
      todoList[3].badge = myTaskNum.value.companyPolicyNum
      todoList[4].badge = myTaskNum.value.electiveNum
      // 学习地图
      todoList[5].badge = myTaskNum.value.journeyNum
      /** Start:有数据的Tab，设置选中状态 */
      // if (newtabButton.value[0].badge > 0) {
      //   activeBtn.value = newtabButton.value[0]?.id
      // } else if (newtabButton.value[1].badge > 0) {
      //   activeBtn.value = newtabButton.value[1]?.id
      // } else if (newtabButton.value[2].badge > 0) {
      //   activeBtn.value = newtabButton.value[2]?.id
      // } else if (newtabButton.value[3].badge > 0) {
      //   activeBtn.value = newtabButton.value[3]?.id
      // } else if (newtabButton.value[4].badge > 0) {
      //   activeBtn.value = newtabButton.value[4]?.id
      // } else if (newtabButton.value[5].badge > 0) {
      //   activeBtn.value = newtabButton.value[5]?.id
      // }
      /** End:有数据的Tab，设置选中状态 */

      myTaskNum.value.total =
        myTaskNum.value?.companyPolicyNum +
        myTaskNum.value?.electiveNum +
        myTaskNum.value?.examNum +
        myTaskNum.value?.onboardingNum +
        myTaskNum.value?.requiredCourseNum +
        myTaskNum.value?.journeyNum
      /** 菜单全部展示 */
      console.log('myTaskNum.value.total====>', myTaskNum.value.total)
      emit('getTotalBadge', myTaskNum.value.total)
      emit('getTotalCourse', myTaskNum.value?.electiveNum + myTaskNum.value?.requiredCourseNum)
      emit('getTotalExam', myTaskNum.value.examNum)
    }
  })
}
getTaskNumber()

// todo切换
const handleChangeNave = (item: Todo) => {
  if (item.component !== activeTodo.value.component) {
    activeTodo.value = item
    getTaskNumber()
  }
}

const getListCaursoul = async () => {
  try {
    loading.value = true
    const data = await listCaursoul({
      pageNum: 1,
      pageSize: 4,
      status: 1
    })
    carouselItems.value = data.list.sort((a, b) => a.sort - b.sort)
  } catch {
  } finally {
    loading.value = false
  }
}

/** 逐个获取待办列表 */
// 获取必修课待办列表
const getMandatoryCourses = async () => {
  try {
    loading.value = true
    const res = await getTodoCourses({
      pageNo: 1,
      pageSize: 3,
      type: 1
    })
    mandatoryCourses.value = res.list
    console.log('MandatoryCourses====>', mandatoryCourses.value)
  } catch {
  } finally {
    loading.value = false
  }
}
// 获取选修课待办列表
const getElectiveCourses = async () => {
  try {
    loading.value = true
    const res = await getTodoCourses({
      pageNo: 1,
      pageSize: 3,
      type: 0
    })
    electiveCourses.value = res
    console.log('ElectiveCourses====>', electiveCourses.value)
  } catch {
  } finally {
    loading.value = false
  }
}

/** Learning duration的数据 */
function getStatisticsInfo() {
  getStatistics().then((res) => {
    statisticsInfo.value = res
  })
}

/** Pengding Course */
function getTotalCourse(course: number) {
  totalPendingCourse.value = course
}

/** Pending Exams的数据 */
function getTotalExam(exam: number) {
  totalPendingExam.value = exam
}

function handleToNews(item: any) {
  router.push({
    name: 'NewsCenter',
    params: {
      newsId: item.id
    }
  })
}

function getTotalBadge(total: number) {
  totalBadge.value = total
}

// 当前激活的 tab
const activeTab = ref(allTabs.value[0])

// 获取 Tab 卡片的样式类和内联样式
const getTabCardClasses = (color: string, index: number) => {
  const isActive = activeTab.value === allTabs.value[index]
  return isActive ? 'border-l-4' : 'border-l-gray-300 bg-white border-l-4'
}

// 获取激活卡片的内联样式
const getTabCardStyle = (color: string, index: number) => {
  const isActive = activeTab.value === allTabs.value[index]

  if (!isActive) {
    return {}
  }

  // 激活状态使用更深的背景色，与图标背景形成对比
  const colorMap = {
    blue: { borderLeftColor: '#2563eb', backgroundColor: '#bfdbfe', color: '#1e3a8a' }, // blue-300
    red: { borderLeftColor: '#dc2626', backgroundColor: '#fca5a5', color: '#7f1d1d' }, // red-300
    green: { borderLeftColor: '#059669', backgroundColor: '#86efac', color: '#064e3b' }, // green-300
    purple: { borderLeftColor: '#7c3aed', backgroundColor: '#c4b5fd', color: '#3730a3' }, // violet-300
    yellow: { borderLeftColor: '#ca8a04', backgroundColor: '#fde047', color: '#713f12' }, // yellow-300
    indigo: { borderLeftColor: '#4f46e5', backgroundColor: '#a5b4fc', color: '#312e81' }, // indigo-300
    orange: { borderLeftColor: '#ea580c', backgroundColor: '#fdba74', color: '#9a3412' }, // orange-300
    amber: { borderLeftColor: '#d97706', backgroundColor: '#fcd34d', color: '#78350f' }, // amber-300
    gray: { borderLeftColor: '#4b5563', backgroundColor: '#d1d5db', color: '#1f2937' } // gray-300
  }

  return colorMap[color] || colorMap.gray
}

// 格式化 Tabs 为 TabContainer 需要的格式
const formattedTabs = computed(() => {
  return allTabs.value.map((tab, index) => ({
    label: tab.name,
    value: tab.component, // 使用 component 名称作为唯一标识
    icon: tab.icon,
    count: tab.badge > 0 ? tab.badge : undefined,
    component: tab.component
  }))
})

// 处理 Tab 变化
const handleTabChange = (tab: any, index: number) => {
  console.log('Tab changed:', tab, index) // 调试信息
  const componentName = tab.value || tab.component
  const originalTab = allTabs.value.find((t) => t.component === componentName)
  if (originalTab) {
    activeTab.value = originalTab
    // 更新所有 tabs 的激活状态
    allTabs.value.forEach((t) => {
      t.active = t.component === componentName
    })
    console.log('Active tab updated:', activeTab.value.component) // 调试信息
  }
}

// 获取当前激活的组件
const getActiveComponent = (componentName: string) => {
  const tab = allTabs.value.find((t) => t.component === componentName)
  return tab?.component || allTabs.value[0]?.component
}

// 当前激活的组件名称
const currentActiveComponent = computed(() => {
  return activeTab.value?.component || allTabs.value[0]?.component
})

// 处理任务卡片点击
const handleTaskCardClick = (task: any) => {
  activeTab.value = task
  // 更新所有 tabs 的激活状态
  allTabs.value.forEach((t) => {
    t.active = t.component === task.component
  })
}

// 获取任务描述
const getTaskDescription = (component: string) => {
  const descriptions = {
    MandatoryCourses: 'Complete your mandatory training courses',
    Exam: 'Take pending exams and assessments',
    Onboarding: 'Complete your onboarding process',
    CompanyPolicy: 'Review and acknowledge company policies',
    Journey: 'Follow your personalized learning journey',
    ElectiveCourses: 'Explore optional courses to enhance your skills',
    Training: 'Attend scheduled training sessions',
    Survey: 'Complete feedback surveys and evaluations'
  }
  return descriptions[component] || 'Manage your learning activities'
}

// 刷新当前任务
const refreshCurrentTask = () => {
  // 这里可以添加刷新逻辑，比如重新获取数据
  console.log('Refreshing task:', activeTab.value.component)
}

// Todo 分类数据
const todoCategories = ref([
  {
    type: 'courses',
    title: 'Mandatory Courses',
    description: 'Required training courses',
    icon: BookOpen,
    color: '#3B82F6',
    tasks: [
      {
        id: 1,
        title: 'Safety Training Fundamentals',
        subtitle: 'Workplace safety basics',
        progress: 75,
        dueDate: '2024-01-15'
      },
      {
        id: 2,
        title: 'Data Privacy & Security',
        subtitle: 'GDPR compliance training',
        progress: 30,
        dueDate: '2024-01-20'
      },
      {
        id: 3,
        title: 'Company Code of Conduct',
        subtitle: 'Ethics and behavior guidelines',
        progress: 0,
        dueDate: '2024-01-25'
      }
    ]
  },
  {
    type: 'exams',
    title: 'Pending Exams',
    description: 'Assessments to complete',
    icon: FileText,
    color: '#EF4444',
    tasks: [
      {
        id: 4,
        title: 'Safety Certification Exam',
        subtitle: 'Final assessment',
        status: 'Due Soon',
        dueDate: '2024-01-18'
      },
      {
        id: 5,
        title: 'Technical Skills Assessment',
        subtitle: 'Quarterly evaluation',
        status: 'Available',
        dueDate: '2024-01-30'
      }
    ]
  },
  {
    type: 'policies',
    title: 'Company Policies',
    description: 'Policies to review',
    icon: Shield,
    color: '#8B5CF6',
    tasks: [
      {
        id: 6,
        title: 'Updated Remote Work Policy',
        subtitle: 'New guidelines effective Jan 2024',
        status: 'New',
        dueDate: '2024-01-22'
      },
      {
        id: 7,
        title: 'IT Security Policy v2.1',
        subtitle: 'Enhanced security measures',
        status: 'Updated',
        dueDate: '2024-01-28'
      }
    ]
  },
  {
    type: 'onboarding',
    title: 'Onboarding Tasks',
    description: 'Complete your setup',
    icon: Users,
    color: '#10B981',
    tasks: []
  },
  {
    type: 'journey',
    title: 'Learning Journey',
    description: 'Personalized path',
    icon: Map,
    color: '#F59E0B',
    tasks: [
      {
        id: 8,
        title: 'Leadership Development Track',
        subtitle: 'Module 2: Communication Skills',
        progress: 60,
        dueDate: '2024-02-05'
      }
    ]
  },
  {
    type: 'training',
    title: 'Live Training',
    description: 'Scheduled sessions',
    icon: Briefcase,
    color: '#F97316',
    tasks: [
      {
        id: 9,
        title: 'Project Management Workshop',
        subtitle: 'Interactive session',
        status: 'Registered',
        dueDate: '2024-01-24'
      }
    ]
  },
  {
    type: 'elective',
    title: 'Elective Courses',
    description: 'Optional skill enhancement',
    icon: GraduationCap,
    color: '#F59E0B',
    tasks: [
      {
        id: 10,
        title: 'Advanced Excel Techniques',
        subtitle: 'Data analysis and visualization',
        progress: 25,
        dueDate: '2024-02-10'
      },
      {
        id: 11,
        title: 'Public Speaking Mastery',
        subtitle: 'Presentation and communication skills',
        progress: 0,
        dueDate: '2024-02-15'
      }
    ]
  },
  {
    type: 'survey',
    title: 'Surveys & Feedback',
    description: 'Complete evaluations',
    icon: ListTodo,
    color: '#10B981',
    tasks: [
      {
        id: 12,
        title: 'Q1 Training Feedback Survey',
        subtitle: 'Rate your learning experience',
        status: 'Pending',
        dueDate: '2024-01-31'
      },
      {
        id: 13,
        title: 'Manager Effectiveness Survey',
        subtitle: 'Anonymous feedback',
        status: 'Available',
        dueDate: '2024-02-05'
      }
    ]
  },
  {
    type: 'certification',
    title: 'Certifications',
    description: 'Professional credentials',
    icon: Award,
    color: '#8B5CF6',
    tasks: [
      {
        id: 14,
        title: 'Safety Officer Certification',
        subtitle: 'Complete all requirements',
        progress: 80,
        dueDate: '2024-02-20'
      }
    ]
  }
])

// 获取任务状态的徽章样式
const getTaskStatusVariant = (status: string) => {
  const variants = {
    'Due Soon': 'destructive',
    Available: 'default',
    New: 'default',
    Updated: 'secondary',
    Registered: 'outline'
  }
  return variants[status] || 'secondary'
}

// 导航到任务详情
const navigateToTask = (task: any, categoryType: string) => {
  console.log('Navigate to task:', task, categoryType)

  // 根据任务类型和具体任务导航
  const taskRoutes = {
    courses: {
      // 课程任务跳转到课程详情页
      path: '/content',
      params: { id: task.id }
    },
    exams: {
      // 考试任务跳转到考试详情页
      path: '/exam',
      params: { id: task.id }
    },
    policies: {
      // 政策任务跳转到政策详情页
      path: '/policy',
      params: { id: task.id }
    },
    onboarding: {
      // 入职任务跳转到入职流程页
      path: '/onboarding',
      query: { taskId: task.id }
    },
    journey: {
      // 学习路径跳转到路径详情
      path: '/journey',
      params: { id: task.id }
    },
    training: {
      // 培训任务跳转到培训详情
      path: '/training',
      params: { id: task.id }
    }
  }

  const routeConfig = taskRoutes[categoryType]
  if (routeConfig) {
    const routeParams: any = { path: routeConfig.path }

    if (routeConfig.params) {
      routeParams.params = routeConfig.params
    }
    if (routeConfig.query) {
      routeParams.query = routeConfig.query
    }

    router.push(routeParams)
  }
}

// 导航到分类页面
const navigateToCategory = (categoryType: string) => {
  console.log('Navigate to category:', categoryType)

  // 映射到 my-center 对应的 tab 和状态
  const myCenterRoutes = {
    courses: {
      path: '/my-center',
      query: {
        tab: 'courses',
        courseType: 'mandatory', // 默认显示必修课
        status: 'in-progress'
      }
    },
    exams: {
      path: '/my-center',
      query: {
        tab: 'exams',
        status: 'pending'
      }
    },
    policies: {
      path: '/my-center',
      query: {
        tab: 'policy',
        status: 'pending'
      }
    },
    onboarding: {
      path: '/my-center',
      query: {
        tab: 'onboarding',
        status: 'in-progress'
      }
    },
    journey: {
      path: '/my-center',
      query: {
        tab: 'records', // 学习记录中查看 journey
        type: 'journey',
        status: 'in-progress'
      }
    },
    training: {
      path: '/my-center',
      query: {
        tab: 'records',
        type: 'training',
        status: 'in-progress'
      }
    }
  }

  const routeConfig = myCenterRoutes[categoryType]
  if (routeConfig) {
    router.push({
      path: routeConfig.path,
      query: routeConfig.query
    })
  } else {
    // 备用方案：直接跳转到对应页面
    const fallbackRoutes = {
      courses: '/content',
      exams: '/exam',
      policies: '/policy',
      onboarding: '/onboarding',
      journey: '/journey',
      training: '/training'
    }

    const fallbackRoute = fallbackRoutes[categoryType]
    if (fallbackRoute) {
      router.push(fallbackRoute)
    }
  }
}

getStatisticsInfo()
getListCaursoul()

onMounted(async () => {
  await getMandatoryCourses()
  await getElectiveCourses()
})
</script>

<template>
  <!-- Banner Section -->
  <div class="mb-4 bg-white rounded-lg border shadow-sm p-4">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- Carousel Section -->
      <div class="lg:col-span-2">
        <div class="rounded-lg overflow-hidden bg-gray-50 h-[280px]">
          <Carousel
            class="relative h-full"
            :plugins="[plugin]"
            :opts="{ loop: true, transition: 5 }"
            @mouseenter="plugin.stop"
            @mouseleave="[plugin.reset(), plugin.play(), console.log('Running')]"
          >
            <CarouselContent class="h-full">
              <CarouselItem
                v-for="item in carouselItems"
                :key="item.id"
                class="cursor-pointer h-full"
                @click="handleToNews(item)"
              >
                <div class="w-full h-full">
                  <img
                    :src="`${item.image}`"
                    class="w-full h-full object-cover"
                    alt="Banner image"
                  />
                </div>
              </CarouselItem>
            </CarouselContent>
            <CarouselPrevious
              class="absolute z-10 -translate-y-1/2 left-4 top-1/2"
              @mouseenter="plugin.stop"
            />
            <CarouselNext
              class="absolute z-10 -translate-y-1/2 right-4 top-1/2"
              @mouseenter="plugin.stop"
            />
          </Carousel>
        </div>
      </div>

      <!-- User Info & Statistics -->
      <div class="lg:col-span-1">
        <div class="bg-gray-50 rounded-lg p-4 h-[280px] flex flex-col">
          <!-- User Profile -->
          <div class="flex items-center gap-4 mb-4 pb-4 border-b border-gray-200">
            <div class="w-16 h-16 rounded-full overflow-hidden bg-white shadow-sm">
              <img
                class="w-full h-full object-cover"
                src="@/assets/images/user.png"
                alt="User avatar"
              />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900 text-lg">
                {{ userStore.user.nickname }}
              </h3>
              <p class="text-sm text-gray-500">Learning Dashboard</p>
            </div>
          </div>

          <!-- Statistics Grid -->
          <div class="flex-1 grid grid-cols-2 gap-2">
            <!-- Learning Duration -->
            <div class="bg-blue-50 border border-blue-100 rounded-lg p-3 flex items-center gap-2">
              <SuperIcon :icon="Clock" size="md" color="blue" />
              <div class="flex-1 min-w-0">
                <div class="text-sm font-bold text-blue-900 truncate">
                  {{ secondsToHHmmss(statisticsInfo.learningDuration) }}
                </div>
                <div class="text-xs text-blue-700">Learning Duration</div>
              </div>
            </div>

            <!-- Pending Courses -->
            <div class="bg-green-50 border border-green-100 rounded-lg p-3 flex items-center gap-2">
              <SuperIcon :icon="BookOpen" size="md" color="green" />
              <div class="flex-1 min-w-0">
                <div class="text-sm font-bold text-green-900">
                  {{ totalPendingCourse }}
                </div>
                <div class="text-xs text-green-700">Pending Courses</div>
              </div>
            </div>

            <!-- Pending Exams -->
            <div
              class="bg-orange-50 border border-orange-100 rounded-lg p-3 flex items-center gap-2 col-span-2"
            >
              <SuperIcon :icon="FileText" size="md" color="orange" />
              <div class="flex-1 min-w-0">
                <div class="text-sm font-bold text-orange-900">
                  {{ totalPendingExam }}
                </div>
                <div class="text-xs text-orange-700">Pending Exams</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions Section -->
  <div class="bg-white rounded-lg border shadow p-4 mb-4">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
      <Button variant="outline" class="h-12 justify-start" @click="$router.push('/content')">
        <Search class="w-4 h-4 mr-2" />
        Browse Courses
      </Button>
      <Button variant="outline" class="h-12 justify-start" @click="$router.push('/exam')">
        <FileText class="w-4 h-4 mr-2" />
        Take Exam
      </Button>
      <Button variant="outline" class="h-12 justify-start" @click="$router.push('/my-center')">
        <User class="w-4 h-4 mr-2" />
        My Progress
      </Button>
      <Button variant="outline" class="h-12 justify-start" @click="$router.push('/journey')">
        <Map class="w-4 h-4 mr-2" />
        Learning Journey
      </Button>
    </div>
  </div>

  <!-- My To-Do Tasks Section -->
  <div class="space-y-4">
    <!-- Section Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold tracking-tight">My To-Do Tasks</h2>
        <p class="text-muted-foreground">Your pending learning tasks and assignments</p>
      </div>
      <Badge variant="outline" class="text-sm">
        {{ todoCategories.reduce((total, cat) => total + cat.tasks.length, 0) }} total tasks
      </Badge>
    </div>

    <!-- Todo Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
      <!-- Todo Cards -->
      <!--必修课-->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- Card Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-full flex items-center justify-center bg-blue-100">
              <CheckCircle class="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900"> Mandatory Courses </h3>
              <p class="text-xs text-muted-foreground"> </p>
            </div>
          </div>
          <Badge variant="destructive" class="text-xs"> 3 </Badge>
          <Badge variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- Task List (Top 3) -->
        <div class="space-y-3 flex-1 mb-4">
          <div
            v-for="course in mandatoryCourses.slice(0, 3)"
            :key="course.id"
            class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            @click="navigateToTask(course, course.type)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1 min-w-0">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ course.name }}</h4>
                <p class="text-xs text-muted-foreground mt-1">{{ course.introduction }}</p>
              </div>
              <!--<div class="ml-3 flex items-center gap-2">-->
              <!--  &lt;!&ndash; Progress indicator &ndash;&gt;-->
              <!--  <div v-if="task.progress !== undefined" class="flex items-center gap-1">-->
              <!--    <div class="w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden">-->
              <!--      <div-->
              <!--        class="h-full bg-green-500 transition-all duration-300"-->
              <!--        :style="{ width: `${task.progress}%` }"-->
              <!--      ></div>-->
              <!--    </div>-->
              <!--    <span class="text-xs text-muted-foreground">{{ task.progress }}%</span>-->
              <!--  </div>-->
              <!--  &lt;!&ndash; Status badge &ndash;&gt;-->
              <!--  <Badge-->
              <!--    v-else-if="task.status"-->
              <!--    :variant="getTaskStatusVariant(task.status)"-->
              <!--    class="text-xs"-->
              <!--  >-->
              <!--    {{ task.status }}-->
              <!--  </Badge>-->
              <!--  &lt;!&ndash; Arrow icon &ndash;&gt;-->
              <!--  <ChevronRight class="w-4 h-4 text-gray-400" />-->
              <!--</div>-->
            </div>
          </div>

          <!-- Empty state -->
          <div v-if="mandatoryCourses.length === 0" class="text-center py-8 text-muted-foreground">
            <CheckCircle class="w-8 h-8 mx-auto mb-2 text-green-500" />
            <p class="text-sm">All tasks completed!</p>
          </div>
        </div>

        <!-- View All Button - 固定在卡片底部 -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory()"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <!--选修课-->
      <Card class="p-6 hover:shadow-lg transition-shadow flex flex-col">
        <!-- Card Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-full flex items-center justify-center bg-blue-100">
              <CheckCircle class="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900"> Elective Courses </h3>
              <p class="text-xs text-muted-foreground"> </p>
            </div>
          </div>
          <Badge variant="destructive" class="text-xs"> 3 </Badge>
          <Badge variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- Task List (Top 3) -->
        <div class="space-y-3 flex-1 mb-4">
          <div
            v-for="course in electiveCourses"
            :key="course.id"
            class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            @click="navigateToTask(course, course.status)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1 min-w-0">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ course.name }}</h4>
                <p class="text-xs text-muted-foreground mt-1">{{ course.introduction }}</p>
              </div>
              <!--<div class="ml-3 flex items-center gap-2">-->
              <!--  &lt;!&ndash; Progress indicator &ndash;&gt;-->
              <!--  <div v-if="task.progress !== undefined" class="flex items-center gap-1">-->
              <!--    <div class="w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden">-->
              <!--      <div-->
              <!--        class="h-full bg-green-500 transition-all duration-300"-->
              <!--        :style="{ width: `${task.progress}%` }"-->
              <!--      ></div>-->
              <!--    </div>-->
              <!--    <span class="text-xs text-muted-foreground">{{ task.progress }}%</span>-->
              <!--  </div>-->
              <!--  &lt;!&ndash; Status badge &ndash;&gt;-->
              <!--  <Badge-->
              <!--    v-else-if="task.status"-->
              <!--    :variant="getTaskStatusVariant(task.status)"-->
              <!--    class="text-xs"-->
              <!--  >-->
              <!--    {{ task.status }}-->
              <!--  </Badge>-->
              <!--  &lt;!&ndash; Arrow icon &ndash;&gt;-->
              <!--  <ChevronRight class="w-4 h-4 text-gray-400" />-->
              <!--</div>-->
            </div>
          </div>

          <!-- Empty state -->
          <div v-if="electiveCourses.length === 0" class="text-center py-8 text-muted-foreground">
            <CheckCircle class="w-8 h-8 mx-auto mb-2 text-green-500" />
            <p class="text-sm">All tasks completed!</p>
          </div>
        </div>

        <!-- View All Button - 固定在卡片底部 -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory()"
          >
            <span class="font-medium">View all</span>
            <div class="flex items-center gap-1">
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>

      <Card
        v-for="todoCategory in todoCategories"
        :key="todoCategory.type"
        class="p-6 hover:shadow-lg transition-shadow flex flex-col"
      >
        <!-- Card Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-full flex items-center justify-center bg-blue-100">
              <CheckCircle class="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">{{ todoCategory.title }}</h3>
              <p class="text-xs text-muted-foreground">{{ todoCategory.description }}</p>
            </div>
          </div>
          <Badge v-if="todoCategory.tasks.length > 0" variant="destructive" class="text-xs">
            {{ todoCategory.tasks.length }}
          </Badge>
          <Badge v-else variant="secondary" class="text-xs">
            <CheckCircle class="w-3 h-3 mr-1" />
            Done
          </Badge>
        </div>

        <!-- Task List (Top 5) -->
        <div class="space-y-3 flex-1 mb-4">
          <div
            v-for="task in todoCategory.tasks.slice(0, 5)"
            :key="task.id"
            class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            @click="navigateToTask(task, todoCategory.type)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1 min-w-0">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ task.title }}</h4>
                <p class="text-xs text-muted-foreground mt-1">{{ task.subtitle }}</p>
              </div>
              <div class="ml-3 flex items-center gap-2">
                <!-- Progress indicator -->
                <div v-if="task.progress !== undefined" class="flex items-center gap-1">
                  <div class="w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      class="h-full bg-green-500 transition-all duration-300"
                      :style="{ width: `${task.progress}%` }"
                    ></div>
                  </div>
                  <span class="text-xs text-muted-foreground">{{ task.progress }}%</span>
                </div>
                <!-- Status badge -->
                <Badge
                  v-else-if="task.status"
                  :variant="getTaskStatusVariant(task.status)"
                  class="text-xs"
                >
                  {{ task.status }}
                </Badge>
                <!-- Arrow icon -->
                <ChevronRight class="w-4 h-4 text-gray-400" />
              </div>
            </div>
          </div>

          <!-- Empty state -->
          <div
            v-if="todoCategory.tasks.length === 0"
            class="text-center py-8 text-muted-foreground"
          >
            <CheckCircle class="w-8 h-8 mx-auto mb-2 text-green-500" />
            <p class="text-sm">All tasks completed!</p>
          </div>
        </div>

        <!-- View All Button - 固定在卡片底部 -->
        <div class="mt-auto pt-4 border-t -mx-6 -mb-6 px-6 pb-4">
          <Button
            variant="outline"
            class="w-full justify-between text-sm h-10"
            @click="navigateToCategory(todoCategory.type)"
          >
            <span class="font-medium">View all {{ todoCategory.title.toLowerCase() }}</span>
            <div class="flex items-center gap-1">
              <span v-if="todoCategory.tasks.length > 5" class="text-xs text-muted-foreground">
                +{{ todoCategory.tasks.length - 5 }} more
              </span>
              <ChevronRight class="w-4 h-4" />
            </div>
          </Button>
        </div>
      </Card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.usercard-number {
  @apply text-base font-bold;
}

.usercard-desc {
  @apply text-[#515151] text-xs;
}
</style>