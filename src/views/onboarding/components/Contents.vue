<script setup lang="ts">
import {
  categoryList,
  onboardingNavList,
  OnboardingNavListVO,
  OnboardingNavReqVO
} from '@/api/onboarding'
import Onboard from '@/views/onboarding/components/onboard/index.vue'

const courseList = ref<Array<OnboardingNavListVO>>([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  title: undefined,
  categoryId: undefined
})
const total = ref(0)
const loading = ref(false)
const categoryData = ref<{ id: number; title: string; sort: number }[]>([])
const nodataShow = ref(false)
const getCategoryList = async () => {
  categoryData.value = await categoryList('')
}
const getOnboardList = async () => {
  courseList.value = []
  const params = {
    ...queryParams.value
  }
  loading.value = true
  try {
    const data = await onboardingNavList(params as OnboardingNavReqVO)
    if (data && data.list && data.list.length > 0) {
      courseList.value = data.list
      total.value = data.total
    } else {
      nodataShow.value = true
    }
  } finally {
    loading.value = false
  }
}
const handleSearch = () => {
  queryParams.value.pageNum = 1
  queryParams.value.pageSize = 20
  nodataShow.value = false
  getOnboardList()
}
const handleReset = () => {
  queryParams.value.pageSize = 20
  queryParams.value.pageNum = 1
  queryParams.value.categoryId = undefined
  queryParams.value.title = ''
  handleSearch()
}

const handleCurrentChange = (newPage: number) => {
  queryParams.value.pageNum = newPage
  getOnboardList()
}

onMounted(() => {
  getOnboardList()
  getCategoryList()
})
</script>

<template>
  <!-- onboarding 查询表单 -->
  <ContainerWrap class="flex space-x-5">
    <div class="flex items-center space-x-2">
      <label for="name">Title</label>
      <Input
        id="name"
        v-model="queryParams.title"
        placeholder="Please input"
        class="w-[320px]"
        clearable
        @keydown.enter="handleSearch"
      />
    </div>
    <div class="flex items-center space-x-2">
      <label for="name">Category</label>
      <Select v-model="queryParams.categoryId">
        <SelectTrigger class="w-[320px]">
          <SelectValue placeholder="Select" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem
              v-for="item in categoryData"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            >
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
    <div class="flex items-center space-x-2">
      <Button @click="handleSearch">
        <Icon name="Search"></Icon>
        Search
      </Button>
      <Button @click="handleReset">
        <Icon name="RefreshCcw"></Icon>
        Reset
      </Button>
    </div>
  </ContainerWrap>
  <!-- Course List -->
  <ContainerWrap :loading="loading">
    <div class="grid grid-cols-5 gap-4 mb-4">
      <div v-for="(item, index) in courseList" :key="`${item.updateBy}_${index}`">
        <Onboard :data="item" />
      </div>
    </div>
    <EmptyPlaceholder v-if="nodataShow" />
    <SmartPagination
      v-show="total > 0"
      :current-page="queryParams.pageNum"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
    />
  </ContainerWrap>
</template>
