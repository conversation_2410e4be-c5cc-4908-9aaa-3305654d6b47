<script setup lang="ts" name="OnboardingDetail">
import { useRoute } from 'vue-router'
import axios from 'axios'
import type { ComponentInternalInstance } from 'vue'
import { VideoPlayer } from '@videojs-player/vue'
import { formatImgUrl } from '@/utils/tool'
import { editStatus, getOnboardingDetail, searchStatus } from '@/api/onboarding'
import 'video.js/dist/video-js.css'
import { ChapterStatus } from '@/enums/chapter'
import { useCounter } from '@/utils/useCountDown'
import { secondsToHHmmss, timestampToDateTime } from '@/utils/date'

const route = useRoute()
// 指引列表
const policyData = ref()
const { t } = useI18n()
const fileType = ref()
const loading = ref(false)
const showVideo = ref(false)
const fileUrl = ref()
const coursePlayData = computed(() => ({
  sources: [
    {
      src: formatImgUrl(fileUrl.value as string),
      type: 'video/mp4'
    }
  ]
}))
const ackBtbShow = ref(false)
// 默认的倒计时事件
const defaultSecond = ref(10)
// 记录一个开始时间
const startTime = ref(+new Date())
// 课程当前的学习状态
const status = ref()
// 记录一个结束时间
const endTime = ref('')
const { start, count } = useCounter()

/** 倒计时 */
const initData = () => {
  start(defaultSecond.value)
}
// }
const getDetail = async () => {
  loading.value = true
  try {
    const data = await getOnboardingDetail(Number(route.params.id))
    policyData.value = data
    if (data.attachmentList && data.attachmentList.length > 0) {
      fileType.value = data.attachmentList[0].fileType
      if (fileType.value === 'pdf' || fileType.value === 'PDF') {
        showVideo.value = false
        if (data.attachmentList[0].fileUrl.includes('https')) {
          axios
            .request({ baseURL: data.attachmentList[0].fileUrl, responseType: 'blob' })
            .then((abc) => {
              const blob = new Blob([abc.data], { type: 'application/pdf' })
              const url = window.URL.createObjectURL(blob)
              fileUrl.value = url
            })
        } else {
          fileUrl.value = formatImgUrl(data.attachmentList[0].fileUrl)
        }
      } else {
        fileUrl.value = data.attachmentList[0].fileUrl
        showVideo.value = true
      }
    }
  } finally {
    loading.value = false
  }
}
/** 查询onboarding的学习进度 */
const progress = async () => {
  if (route.query.isMandatory === 'true') {
    const data = await searchStatus(Number(route.params.id))
    if (data) {
      status.value = data.status
      if (data.status !== ChapterStatus.Completed) {
        defaultSecond.value = data.duration
        initData()
      }
    }
  }
}
const studyStatus = watch(
  () => status.value,
  (newValue) => {
    if (newValue === ChapterStatus.NotStart) {
      studyStatus()
      /** 修改onboarding状态 */
      editStatus({ onBoardingId: Number(route.params.id), status: ChapterStatus.InProgress })
    }
  }
)
const watcher = watch(
  () => count.value,
  (newValue) => {
    if (newValue === 0) {
      watcher()
      endTime.value = timestampToDateTime(+new Date() / 1000)
      /** 修改onboarding状态 */
      editStatus({ onBoardingId: Number(route.params.id), status: ChapterStatus.Completed })
    }
  }
)

onMounted(() => {
  getDetail()
  progress()
})
</script>

<template>
  <ContainerWrap border v-loading="loading">
    <!-- 课程标题 -->
    <h1 class="font-bold mb-4 text-olpblack text-3xl">
      {{ policyData?.title }}
    </h1>
    <!-- 倒计时确认按钮 -->
    <div>
      <div
        class="w-[82px] h-[24px] rounded-[4px] bg-black/50 text-sm text-white flex-vertical-center absolute top-2.5 right-0 z-50"
      >
        {{ secondsToHHmmss(count) }}
      </div>
    </div>
  </ContainerWrap>
  <ContainerWrap>
    <!-- 有文件时 -->
    <!-- 视频 -->
    <VideoPlayer
      v-if="showVideo"
      :seeking="false"
      class="video-player vjs-big-play-centered"
      controls
      playsinline
      :sources="coursePlayData.sources"
      :poster="formatImgUrl(policyData?.cover)"
      crossorigin="anonymous"
      :width="1400"
      :height="600"
    />
    <!-- pdf文档 -->
    <iframe
      v-else
      :src="resqUrl"
      class="w-full border-b border-gray-300 min-h-[inherit]"
      allow="fullscreen"
    />
    <!-- 内容介绍 -->
    <p
      class="mt-7 text-sm text-olplightblack break-normal whitespace-normal"
      v-html="policyData?.description?.replace(/\n/g, '<br>')"
    />
  </ContainerWrap>
</template>

<style scoped lang="scss"></style>
