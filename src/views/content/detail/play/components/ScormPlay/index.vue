<script setup lang="ts">
import dayjs from 'dayjs'
import Duration from 'dayjs/plugin/duration'

import Custom12API from './scripts/custom12'
import Custom2004API from './scripts/custom2004'
import { getAccessToken } from '@/utils/auth'
import { ScormFileStatus } from '@/enums/chapter'
import { useLocaleStore } from '@/store/modules/locale'
import { useUserStore } from '@/store/modules/user'
import type { CourseChapter, CourseDetail, ScromVersion } from '@/api/course/details'
import { getPlay, getRecord, updateRecord } from '@/api/course/play'
import { StudyRecord } from '@/types/content/detail-play'
const props = defineProps<{
  data: CourseChapter
  courseData: CourseDetail | undefined
  chapterId: any
  courseId: any
  filePreviewConfig: number
}>()

dayjs.extend(Duration)
const localeStore = useLocaleStore()
const currentLocale = computed(() => localeStore.getCurrentLocale)
// const 设置语言对应的后台请求头
const lang: Record<string, string> = {
  en: 'en_US',
  ar: 'ar_iq'
}
const loading = ref(false)
const scormLoading = ref(true)
// 当前课程是否正在更新中
const isUpdating = ref(ScormFileStatus.Success)
// 当前播放scorm课程的链接
// const scormLink = ref('/scormother/iso-9001-2015-qms-awareness-aicc-czUdTmIk/scormdriver/indexAPI.html?AICC_URL=/test&AICC_SID=123456789')
/** scorm1.2测试链接 */
// const scormLink = ref('/scorm/scorm12/2b8f11c2078c4f8c96911e4ba993d347/scormdriver/indexAPI.html')
// const scormLink = ref('/scorm20044th/scormdriver/indexAPI.html')
const scormLink = ref('')

/**
 * 当前scorm版本
 */
// const scormVersion = ref<ScromVersion>(props.data.scormVersion as ScromVersion)
const scormVersion = ref<ScromVersion>(props.data.scormVersion as ScromVersion)

const isAssign = ref(1)

// 是否scorm全屏
const isFullScreen = ref(false)

const isMounted = ref(false)

// 课程当前的学习状态
const status = ref(props.data.status)
const recordData = computed<StudyRecord>(() => ({
  courseChapterId: props.chapterId,
  courseId: props.courseId,
  status: status.value === null ? 1 : status.value
}))

const iframeRef = ref()
// 将时分秒转换为秒
function formatSconds(timeString: string) {
  if (scormVersion.value === '1.2') {
    // 使用split方法将时分秒字符串拆分为数组
    const timeArray = timeString.split(':')

    // 将数组元素转换为数字，并计算总秒数
    const seconds = +timeArray[0] * 3600 + +timeArray[1] * 60 + +timeArray[2]
    return seconds
  } else {
    return Math.round(dayjs.duration(timeString).asSeconds())
  }
}
/** 自动保存进度操作(1.2) */
function submitUserData({ cmi }: { cmi: any }) {
  cmi.core.student_id = useUserStore().user!.id || ''
  cmi.core.student_name = useUserStore().user!.nickname || ''

  return {
    courseId: props.courseId,
    courseChapterId: props.chapterId,
    scormProgress: isAssign.value === 1 ? JSON.stringify(cmi) : ''
    // studyTime: formatSconds(cmi.core.total_time),
    // status: cmi.core.lesson_status,
    // score: +cmi.core.score.raw,
  }
}
/** 自动保存进度操作(2004) */
function submitUserData2004({ cmi }: { cmi: any }) {
  cmi.learner_id = useUserStore().user?.id || ''
  cmi.learner_name = useUserStore().user?.nickname || ''
  return {
    courseId: props.courseId,
    courseChapterId: props.chapterId,
    scormProgress: isAssign.value === 1 ? JSON.stringify(cmi) : ''
    // studyTime: formatSconds(cmi.total_time),
    // status: cmi.success_status === 'unknown' ? 'incomplete' : cmi.success_status,
    // score: +cmi.score.raw,
  }
}
function initLMS(json: any) {
  // 获取当前scorm版本信息，根据不同的版本，初始化不同的API
  if (scormVersion.value === '1.2') {
    window.API = new Custom12API({
      logLevel: 5,
      autocommit: isAssign.value === 1,
      autocommitSeconds: 5,
      asyncCommit: true,
      dataCommitFormat: 'json',
      mastery_override: true,
      selfReportSessionTime: true,
      alwaysSendTotalTime: true,
      lmsCommitUrl:
        isAssign.value === 1
          ? `${import.meta.env.VITE_APP_BASE_API}/learning/course/study/progress`
          : undefined,
      xhrHeaders:
        isAssign.value === 1
          ? {
              Authorization: getAccessToken(),
              Lang: currentLocale.value.lang
            }
          : undefined,
      requestHandler: isAssign.value === 1 ? submitUserData : undefined
    })
    try {
      window.API.loadFromJSON(json)
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  }
  // 否则全部默认为2004版本
  else {
    // 初始化2004API
    window.API_1484_11 = new Custom2004API({
      logLevel: 5,
      autocommit: isAssign.value === 1,
      autocommitSeconds: 5,
      asyncCommit: true,
      dataCommitFormat: 'json',
      mastery_override: true,
      selfReportSessionTime: true,
      alwaysSendTotalTime: true,
      lmsCommitUrl:
        isAssign.value === 1
          ? `${import.meta.env.VITE_APP_BASE_API}/learning/course/study/progress`
          : undefined,
      xhrHeaders:
        isAssign.value === 1
          ? {
              Authorization: getAccessToken(),
              Lang: currentLocale.value.lang
            }
          : undefined,
      requestHandler: isAssign.value === 1 ? submitUserData2004 : undefined
      // responseHandler: isAssign.value === 1 ? scormRes('API_1484_11') : undefined,
    })
    window.API_1484_11.loadFromJSON(json)
  }
}
/** 获取当前进度信息，用于恢复scorm进度 */
function getScormRecord() {
  // 当type为7 证明是aicc课件,取不同的参数值
  if (props.data.type === 7) {
    // 通过配置项来进行预览
    if (props.filePreviewConfig === 1) {
      console.log('props.data.aiccRunPath', props.data.aiccRunPath)
      scormLink.value = props.data.aiccRunPath
    } else {
      window.open(props.data.aiccRunPath, '_blank')
    }
  } else {
    getRecord(props.courseId, props.chapterId).then((res) => {
      let scormProgress = res?.scormProgress
      if (!scormProgress) {
        // 如果没有进度信息，暂时初始化
        scormProgress =
          scormVersion.value === '1.2'
            ? {
                core: {
                  student_id: useUserStore().user!.id || '',
                  student_name: useUserStore().user!.nickname || ''
                }
              }
            : {
                learner_id: useUserStore().user!.id || '',
                learner_name: useUserStore().user!.nickname || ''
              }
      } else {
        scormProgress = JSON.parse(scormProgress)
      }
      initLMS(scormProgress)
      scormLink.value = props.data.scormRunPath
    })
  }
}

const updateAICCRecord = async () => {
  if (props.data.type === 7) {
    const data = await getPlay(props.chapterId)
    status.value = data.status
    await updateRecord(recordData.value)
  }
}

function updateAICCRecordSync() {
  if (props.data.type === 7) {
    const data = recordData.value
    const body = JSON.stringify(data)
    const url = `${import.meta.env.VITE_APP_BASE_API}/learning/course/study/progress`

    const xhr = new XMLHttpRequest()
    xhr.open('POST', url, true)
    xhr.setRequestHeader('Content-Type', 'application/json')
    xhr.setRequestHeader('Authorization', getAccessToken())
    xhr.send(body)
  }
}

function iframeOnload() {
  scormLoading.value = false
}
onMounted(() => {
  getScormRecord()
  // 同步记录
  updateAICCRecord()
  window.addEventListener('beforeunload', updateAICCRecordSync)
})

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', updateAICCRecordSync)
})
</script>

<template>
  <div class="flex w-full h-full overflow-hidden">
    <!-- 鼠标悬浮，显示回退按钮 -->
    <!-- 右侧内容区域 -->
    <template v-if="[ScormFileStatus.Failure, ScormFileStatus.Unziping].includes(isUpdating)">
      <div class="w-full h-full bg-[#6C6C6C] flex items-center justify-center">
        <div class="flex flex-col items-center justify-center">
          <div class="banter-loader">
            <div class="banter-loader__box" />
            <div class="banter-loader__box" />
            <div class="banter-loader__box" />
            <div class="banter-loader__box" />
            <div class="banter-loader__box" />
            <div class="banter-loader__box" />
            <div class="banter-loader__box" />
            <div class="banter-loader__box" />
            <div class="banter-loader__box" />
          </div>
          <h1 class="mt-5 text-4xl text-white">
            {{ $t('scorm.play.updatingTip') }}
          </h1>
        </div>
      </div>
    </template>
    <template v-else>
      <div
        id="scorm-container"
        v-loading="loading"
        element-loading-background="rgba(0,0,0,0.7)"
        class="flex-1 bg-[#6C6C6C]"
        :class="!isFullScreen ? 'relative' : ''"
      >
        <!-- 全屏与非全屏按钮 -->
        <div
          class="top-3.5 right-3.5 absolute cursor-pointer z-2001 w-6 h-6 bg-black/50 rounded-[4px] flex-vertical-center"
          @click="isFullScreen = !isFullScreen"
        >
          <svg-icon :icon-class="isFullScreen ? 'FullScreenExit' : 'FullScreen'" />
        </div>
        <!-- <Teleport v-if="isMounted" :to="isFullScreen ? 'body' : '#scorm-container'"> -->
        <div
          v-loading="scormLoading"
          element-loading-text="The course is loading"
          :class="
            isFullScreen
              ? 'absolute top-0 w-screen h-screen z-40 inset-0 bg-[#6C6C6C]'
              : 'w-full h-full'
          "
        >
          <iframe
            ref="iframeRef"
            :key="scormLink"
            class="w-full h-full"
            :src="scormLink"
            :onload="iframeOnload"
          />
        </div>
        <!-- </Teleport> -->
        <!-- :src="scormLink" -->
      </div>
    </template>
  </div>
</template>

<style lang="scss">
.custom-scorm-notification {
  .el-notification__content {
    text-align: left;
  }
}
/**正在更新中的样式 */
.banter-loader {
  width: 72px;
  height: 72px;
  margin-left: -36px;
  margin-top: -36px;
}
.banter-loader__box {
  float: left;
  position: relative;
  width: 20px;
  height: 20px;
  margin-right: 6px;
}
.banter-loader__box:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #fff;
}
.banter-loader__box:nth-child(3n) {
  margin-right: 0;
  margin-bottom: 6px;
}
.banter-loader__box:nth-child(1):before,
.banter-loader__box:nth-child(4):before {
  margin-left: 26px;
}
.banter-loader__box:nth-child(3):before {
  margin-top: 52px;
}
.banter-loader__box:last-child {
  margin-bottom: 0;
}

@-webkit-keyframes moveBox-1 {
  9.0909090909% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  54.5454545455% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  63.6363636364% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  72.7272727273% {
    -webkit-transform: translate(26px, 0px);
    transform: translate(26px, 0px);
  }
  81.8181818182% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
  90.9090909091% {
    -webkit-transform: translate(-26px, 0px);
    transform: translate(-26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@keyframes moveBox-1 {
  9.0909090909% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  54.5454545455% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  63.6363636364% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  72.7272727273% {
    -webkit-transform: translate(26px, 0px);
    transform: translate(26px, 0px);
  }
  81.8181818182% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
  90.9090909091% {
    -webkit-transform: translate(-26px, 0px);
    transform: translate(-26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}
.banter-loader__box:nth-child(1) {
  -webkit-animation: moveBox-1 4s infinite;
  animation: moveBox-1 4s infinite;
}

@-webkit-keyframes moveBox-2 {
  9.0909090909% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  54.5454545455% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  63.6363636364% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  72.7272727273% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  81.8181818182% {
    -webkit-transform: translate(0px, 26px);
    transform: translate(0px, 26px);
  }
  90.9090909091% {
    -webkit-transform: translate(0px, 26px);
    transform: translate(0px, 26px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@keyframes moveBox-2 {
  9.0909090909% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  54.5454545455% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  63.6363636364% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  72.7272727273% {
    -webkit-transform: translate(26px, 26px);
    transform: translate(26px, 26px);
  }
  81.8181818182% {
    -webkit-transform: translate(0px, 26px);
    transform: translate(0px, 26px);
  }
  90.9090909091% {
    -webkit-transform: translate(0px, 26px);
    transform: translate(0px, 26px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}
.banter-loader__box:nth-child(2) {
  -webkit-animation: moveBox-2 4s infinite;
  animation: moveBox-2 4s infinite;
}

@-webkit-keyframes moveBox-3 {
  9.0909090909% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  54.5454545455% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  63.6363636364% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  72.7272727273% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  81.8181818182% {
    -webkit-transform: translate(-26px, -26px);
    transform: translate(-26px, -26px);
  }
  90.9090909091% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@keyframes moveBox-3 {
  9.0909090909% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  54.5454545455% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  63.6363636364% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  72.7272727273% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  81.8181818182% {
    -webkit-transform: translate(-26px, -26px);
    transform: translate(-26px, -26px);
  }
  90.9090909091% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}
.banter-loader__box:nth-child(3) {
  -webkit-animation: moveBox-3 4s infinite;
  animation: moveBox-3 4s infinite;
}

@-webkit-keyframes moveBox-4 {
  9.0909090909% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(-26px, -26px);
    transform: translate(-26px, -26px);
  }
  36.3636363636% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
  54.5454545455% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  63.6363636364% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  72.7272727273% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  81.8181818182% {
    -webkit-transform: translate(-26px, -26px);
    transform: translate(-26px, -26px);
  }
  90.9090909091% {
    -webkit-transform: translate(-26px, 0px);
    transform: translate(-26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@keyframes moveBox-4 {
  9.0909090909% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(-26px, -26px);
    transform: translate(-26px, -26px);
  }
  36.3636363636% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
  54.5454545455% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  63.6363636364% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  72.7272727273% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  81.8181818182% {
    -webkit-transform: translate(-26px, -26px);
    transform: translate(-26px, -26px);
  }
  90.9090909091% {
    -webkit-transform: translate(-26px, 0px);
    transform: translate(-26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}
.banter-loader__box:nth-child(4) {
  -webkit-animation: moveBox-4 4s infinite;
  animation: moveBox-4 4s infinite;
}

@-webkit-keyframes moveBox-5 {
  9.0909090909% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  54.5454545455% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  63.6363636364% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  72.7272727273% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  81.8181818182% {
    -webkit-transform: translate(26px, -26px);
    transform: translate(26px, -26px);
  }
  90.9090909091% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@keyframes moveBox-5 {
  9.0909090909% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  54.5454545455% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  63.6363636364% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  72.7272727273% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  81.8181818182% {
    -webkit-transform: translate(26px, -26px);
    transform: translate(26px, -26px);
  }
  90.9090909091% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}
.banter-loader__box:nth-child(5) {
  -webkit-animation: moveBox-5 4s infinite;
  animation: moveBox-5 4s infinite;
}

@-webkit-keyframes moveBox-6 {
  9.0909090909% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  54.5454545455% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  63.6363636364% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  72.7272727273% {
    -webkit-transform: translate(0px, 26px);
    transform: translate(0px, 26px);
  }
  81.8181818182% {
    -webkit-transform: translate(-26px, 26px);
    transform: translate(-26px, 26px);
  }
  90.9090909091% {
    -webkit-transform: translate(-26px, 0px);
    transform: translate(-26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@keyframes moveBox-6 {
  9.0909090909% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  54.5454545455% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  63.6363636364% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  72.7272727273% {
    -webkit-transform: translate(0px, 26px);
    transform: translate(0px, 26px);
  }
  81.8181818182% {
    -webkit-transform: translate(-26px, 26px);
    transform: translate(-26px, 26px);
  }
  90.9090909091% {
    -webkit-transform: translate(-26px, 0px);
    transform: translate(-26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}
.banter-loader__box:nth-child(6) {
  -webkit-animation: moveBox-6 4s infinite;
  animation: moveBox-6 4s infinite;
}

@-webkit-keyframes moveBox-7 {
  9.0909090909% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  54.5454545455% {
    -webkit-transform: translate(26px, -26px);
    transform: translate(26px, -26px);
  }
  63.6363636364% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  72.7272727273% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  81.8181818182% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
  90.9090909091% {
    -webkit-transform: translate(26px, 0px);
    transform: translate(26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@keyframes moveBox-7 {
  9.0909090909% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(26px, 0);
    transform: translate(26px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  54.5454545455% {
    -webkit-transform: translate(26px, -26px);
    transform: translate(26px, -26px);
  }
  63.6363636364% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  72.7272727273% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  81.8181818182% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
  90.9090909091% {
    -webkit-transform: translate(26px, 0px);
    transform: translate(26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}
.banter-loader__box:nth-child(7) {
  -webkit-animation: moveBox-7 4s infinite;
  animation: moveBox-7 4s infinite;
}

@-webkit-keyframes moveBox-8 {
  9.0909090909% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(-26px, -26px);
    transform: translate(-26px, -26px);
  }
  36.3636363636% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  54.5454545455% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  63.6363636364% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  72.7272727273% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  81.8181818182% {
    -webkit-transform: translate(26px, -26px);
    transform: translate(26px, -26px);
  }
  90.9090909091% {
    -webkit-transform: translate(26px, 0px);
    transform: translate(26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}

@keyframes moveBox-8 {
  9.0909090909% {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(-26px, -26px);
    transform: translate(-26px, -26px);
  }
  36.3636363636% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  54.5454545455% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  63.6363636364% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  72.7272727273% {
    -webkit-transform: translate(0px, -26px);
    transform: translate(0px, -26px);
  }
  81.8181818182% {
    -webkit-transform: translate(26px, -26px);
    transform: translate(26px, -26px);
  }
  90.9090909091% {
    -webkit-transform: translate(26px, 0px);
    transform: translate(26px, 0px);
  }
  100% {
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
}
.banter-loader__box:nth-child(8) {
  -webkit-animation: moveBox-8 4s infinite;
  animation: moveBox-8 4s infinite;
}

@-webkit-keyframes moveBox-9 {
  9.0909090909% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  54.5454545455% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  63.6363636364% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  72.7272727273% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  81.8181818182% {
    -webkit-transform: translate(-52px, 0);
    transform: translate(-52px, 0);
  }
  90.9090909091% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  100% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
}

@keyframes moveBox-9 {
  9.0909090909% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  18.1818181818% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  27.2727272727% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  36.3636363636% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  45.4545454545% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  54.5454545455% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
  63.6363636364% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  72.7272727273% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  81.8181818182% {
    -webkit-transform: translate(-52px, 0);
    transform: translate(-52px, 0);
  }
  90.9090909091% {
    -webkit-transform: translate(-26px, 0);
    transform: translate(-26px, 0);
  }
  100% {
    -webkit-transform: translate(0px, 0);
    transform: translate(0px, 0);
  }
}
.banter-loader__box:nth-child(9) {
  -webkit-animation: moveBox-9 4s infinite;
  animation: moveBox-9 4s infinite;
}
</style>
