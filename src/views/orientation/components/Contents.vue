<script setup lang="ts">
import {
  categoryList,
  orientationNavList,
  OrientationNavListVO,
  OrientationNavReqVO
} from '@/api/orientation'
import Orientation from '@/views/orientation/components/orien/index.vue'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { RefreshCcw } from 'lucide-vue-next'
import { SuperSearch } from '@/components/SuperSearch'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import EmptyPlaceholder from '@/components/EmptyPlaceholder.vue'
import { SmartPagination } from '@/components/SmartPagination'

const courseList = ref<Array<OrientationNavListVO>>([])
const queryParams = ref<OrientationNavReqVO>({
  pageNum: 1,
  pageSize: 20,
  title: '',
  categoryId: undefined
})
const total = ref(0)
const loading = ref(false)
const categoryData = ref<{ id: number; title: string; sort: number }[]>([])
const nodataShow = ref(false)
const getCategoryList = async () => {
  categoryData.value = await categoryList('')
}
const getOrientationList = async () => {
  const params = {
    ...queryParams.value
  }
  try {
    loading.value = true
    const data = await orientationNavList(params as OrientationNavReqVO)
    if (data && data.list) {
      courseList.value = data.list
      total.value = data.total
      if (data.list.length === 0) {
        nodataShow.value = true
      }
    } else {
      nodataShow.value = true
    }
  } finally {
    loading.value = false
  }
}
const handleSearch = () => {
  queryParams.value.pageNum = 1
  queryParams.value.pageSize = 20
  nodataShow.value = false
  getOrientationList()
}
const handleReset = () => {
  queryParams.value.pageSize = 20
  queryParams.value.pageNum = 1
  queryParams.value.categoryId = undefined
  queryParams.value.title = ''
  handleSearch()
}

const handleCurrentChange = (newPage: number) => {
  queryParams.value.pageNum = newPage
  getOrientationList()
}

onMounted(() => {
  getOrientationList()
  getCategoryList()
})
</script>

<template>
  <!-- onboarding 查询表单 -->
  <ContainerWrapper class="flex space-x-5">
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="h-full flex items-center justify-between px-6 py-4 bg-white border-b">
            <!-- Filter Section -->
            <div class="flex items-center gap-4">
              <!-- Search Input -->
              <div class="flex items-center gap-2">
                <SuperSearch
                  v-model="queryParams.title"
                  placeholder="Search policies..."
                  @search="handleSearch"
                  clearable
                  @clear="queryParams.title = ''"
                  @keyup="handleSearch"
                  class="w-full max-w-md"
                />

                <!-- Category Select -->
                <Select v-model="queryParams.categoryId">
                  <SelectTrigger class="w-[240px]">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem :value="undefined" label="All Categories">
                        All Categories
                      </SelectItem>
                      <SelectItem
                        v-for="item in categoryData"
                        :key="item.id"
                        :label="item.title"
                        :value="item.id"
                      >
                        {{ item.title }}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center gap-2">
              <Button variant="outline" size="sm" @click="handleReset" class="text-xs">
                <RefreshCcw class="w-4 h-4 mr-1" />
                Reset
              </Button>
            </div>
          </div>
        </template>
        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 p-6"
        >
          <div
            v-for="(item, index) in courseList"
            :key="`${item.updateBy}_${index}`"
            class="h-full"
          >
            <Orientation :data="item" />
          </div>
        </div>
        <EmptyPlaceholder v-if="nodataShow" />
        <template #footer v-if="total > 0">
          <SmartPagination
            :current-page="queryParams.pageNum"
            :page-size="queryParams.pageSize"
            :total="total"
            @current-change="handleCurrentChange"
          />
        </template>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss"></style>
