<script setup lang="ts" name="Onboard">
import { formatImgUrl } from '@/utils/tool'
import { useRouter } from 'vue-router'
import { Badge } from '@/components/ui/badge'
import { Calendar, User, Eye } from 'lucide-vue-next'

const router = useRouter()
const props = defineProps({
  update: Function,
  data: {
    type: Object,
    default: () => ({})
  }
})

const isClickable = computed(() => {
  return props.data.fileTypeList || props.data.attachmentList
})

const handleClick = () => {
  if (isClickable.value) {
    router.push({ name: 'OrientationDetail', params: { id: props.data.id } })
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>

<template>
  <div
    class="group relative overflow-hidden rounded-lg border bg-card hover:shadow-lg transition-all duration-200"
    :class="[isClickable ? 'cursor-pointer' : 'cursor-not-allowed opacity-60']"
    @click="handleClick"
  >
    <!-- Image Area -->
    <div class="h-32 relative overflow-hidden bg-gradient-to-br from-slate-100 to-slate-200">
      <LazyImage
        :src="props.data.cover"
        :alt="props.data?.title || props.data?.name"
        :aspect-ratio="'16/9'"
        class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
      />

      <!-- Status Badge -->
      <div class="absolute top-2 right-2">
        <Badge
          :variant="isClickable ? 'default' : 'secondary'"
          class="text-xs font-medium px-2 py-1"
        >
          {{ isClickable ? 'Available' : 'Coming Soon' }}
        </Badge>
      </div>

      <!-- Category Badge -->
      <div v-if="props.data.categoryName" class="absolute top-2 left-2">
        <Badge variant="outline" class="text-xs font-medium px-2 py-1 bg-white/90">
          {{ props.data.categoryName }}
        </Badge>
      </div>
    </div>

    <!-- Content Area -->
    <div class="p-4">
      <!-- Title -->
      <h3 class="font-medium text-sm mb-2 line-clamp-2 group-hover:text-primary transition-colors">
        {{ props.data.title || props.data.name || 'Untitled' }}
      </h3>

      <!-- Description -->
      <p v-if="props.data.description" class="text-xs text-muted-foreground mb-3 line-clamp-2">
        {{ props.data.description }}
      </p>

      <!-- Metadata -->
      <div class="flex items-center justify-between text-xs text-muted-foreground">
        <div class="flex items-center gap-2">
          <div v-if="props.data.updateTime" class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatDate(props.data.updateTime) }}</span>
          </div>
          <span v-if="props.data.updateTime && props.data.creatorName">•</span>
          <div v-if="props.data.creatorName" class="flex items-center gap-1">
            <User class="w-3 h-3" />
            <span>{{ props.data.creatorName }}</span>
          </div>
        </div>
        <div v-if="isClickable" class="flex items-center gap-1">
          <Eye class="w-3 h-3" />
          <span>View</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
