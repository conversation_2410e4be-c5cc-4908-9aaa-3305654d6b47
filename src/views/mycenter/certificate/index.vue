<script setup lang="ts" name="MyCertificate">
import {
  CertificateExpireTypeEnum,
  CertificateStatusEnum,
  MyCertificateApi
} from '@/api/mycenter/mycertificate'
import { useUserStore } from '@/store/modules/user'
import { formatDate } from '@/utils/formatTime'
import CertificateGrid from './components/CertificateGrid.vue'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import TabNav from '@/components/TabNav.vue'
import { BookOpen } from 'lucide-vue-next'

// 路由相关
const route = useRoute()
const router = useRouter()

interface CertificateTab {
  key: string
  label: string
  value: CertificateStatusEnum
}

const userStore = useUserStore()
const loading = ref(false)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: userStore.user.id
})
const certificateList = ref([])
const total = ref(0)
// 从URL参数初始化状态
const selectedType = ref<string>((route.query.status as string) || 'all')

// Certificate tabs following the same pattern as course tabs
const certificateTabs: CertificateTab[] = [
  {
    key: 'all',
    label: 'All',
    value: undefined as any
  },
  {
    key: 'expired',
    label: 'Expired',
    value: CertificateStatusEnum.EXPIRED
  },
  {
    key: 'valid',
    label: 'Valid',
    value: CertificateStatusEnum.VALID
  }
]

/** Handle status change */
const handleChangeType = (type: string) => {
  if (!type) return // ToggleGroup can return undefined when deselecting

  selectedType.value = type
  queryParams.pageNo = 1 // Reset to first page when changing status

  // 同步URL参数
  router.push({
    path: '/my-center/index',
    query: {
      tab: 'certificate',
      status: type
    }
  })

  getCertificateList()
}

/** Get certificate list */
const getCertificateList = async () => {
  loading.value = true
  try {
    const res = await MyCertificateApi.getMyCertificatePage(queryParams)
    const currentTime = new Date()
    const certificationData = res.list?.map((item) => {
      const formattedTime = formatDate(item.expiresTime)
      const expireDate = new Date(formattedTime.replace(' ', 'T'))
      const expiresType =
        expireDate < currentTime
          ? CertificateExpireTypeEnum.EXPIRED
          : CertificateExpireTypeEnum.VALID
      return {
        ...item,
        expiresType
      }
    })

    // Filter based on selected type
    const selectedTab = certificateTabs.find(tab => tab.key === selectedType.value)
    const statusValue = selectedTab?.value

    if (selectedType.value === 'all' || statusValue === undefined) {
      // Show all certificates
      certificateList.value = certificationData || []
    } else {
      // Filter by specific status
      certificateList.value = certificationData?.filter(
        (item) => item.expiresType === statusValue
      ) || []
    }
    total.value = certificateList.value?.length
  } finally {
    loading.value = false
  }
}

const handleCurrentChange = (newPage: number) => {
  queryParams.pageNo = newPage
  getCertificateList()
}

// Handle certificate click
const handleCertificateClick = (certificate: any) => {
  console.log('Certificate clicked:', certificate)
  // Add navigation logic here if needed
}

// Computed property for current tab label
const currentTabLabel = computed(() => {
  const tab = certificateTabs.find(t => t.key === selectedType.value)
  return tab?.label || 'Expired'
})

onMounted(() => {
  // 如果没有URL参数，默认跳转到all状态
  if (!route.query.status) {
    router.replace({
      path: '/my-center/index',
      query: {
        tab: 'certificate',
        status: 'all'
      }
    })
    return
  }

  getCertificateList()
})
</script>

<template>
  <ContainerScroll>
    <!-- Header with TabNav and Status Filter -->
    <template #header>
      <div class="px-6 py-3 w-full h-full flex items-center">
        <div class="flex items-center justify-between w-full">
          <!-- Left: TabNav for "All My Certificate" -->
          <TabNav
            :tabs="[{ key: 'all', label: 'All My Certificate' }]"
            :active-tab="'all'"
          />

          <!-- Right: Status Toggle Group -->
          <ToggleGroup
            v-model="selectedType"
            type="single"
            size="sm"
            class="space-x-1"
            @update:model-value="handleChangeType"
          >
            <ToggleGroupItem
              v-for="tab in certificateTabs"
              :key="tab.key"
              :value="tab.key"
              class="px-3"
            >
              {{ tab.label }}
            </ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>
    </template>

    <!-- Scrollable Content -->
    <div class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="p-6">
          <CertificateGrid
            :certificate-list="certificateList"
            :loading="loading"
            :current-tab-label="currentTabLabel"
            @certificate-click="handleCertificateClick"
          />
        </div>
      </ScrollArea>
    </div>

    <!-- Fixed Pagination at Bottom -->
    <template #footer v-if="total > 0">
      <div class="px-6 py-4 w-full h-full flex items-center">
        <SmartPagination
          :total="total"
          :current-page="queryParams.pageNo"
          :page-size="queryParams.pageSize"
          @current-change="handleCurrentChange"
        />
      </div>
    </template>
  </ContainerScroll>
</template>

<style scoped lang="scss"></style>
