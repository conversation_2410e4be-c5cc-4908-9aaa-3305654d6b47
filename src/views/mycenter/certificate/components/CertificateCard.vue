<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { formatDatestamp } from '@/utils/formatTime'
import { CertificateExpireTypeEnum, CertificateTypeEnum } from '@/api/mycenter/mycertificate'
import { Award, Calendar, ExternalLink, RotateCcw } from 'lucide-vue-next'

interface Props {
  certificate: any
}

interface Emits {
  (e: 'click', certificate: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const router = useRouter()

// Get certificate status
const certificateStatus = computed(() => {
  if (props.certificate.expiresType === CertificateExpireTypeEnum.EXPIRED) {
    return { label: 'Expired', color: 'text-red-500', variant: 'destructive' as const }
  } else {
    return { label: 'Valid', color: 'text-green-500', variant: 'default' as const }
  }
})

// Handle certificate click
const handleClick = () => {
  if (props.certificate.type === CertificateTypeEnum.JOIN_TRAINING) {
    router.push({ name: 'TrainingDetail', query: { id: props.certificate.taskId } })
  }
  emit('click', props.certificate)
}

// Preview certificate
const handlePreview = (event: Event) => {
  event.stopPropagation()
  window.open(props.certificate.image, '_blank')
}

// Handle renew action
const handleRenew = (event: Event) => {
  event.stopPropagation()
  handleClick()
}
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 overflow-hidden group cursor-pointer" @click="handlePreview">
    <div class="flex items-center p-4 gap-4">
      <!-- Certificate Image -->
      <div class="relative w-24 h-24 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg overflow-hidden flex-shrink-0">
        <img
          v-if="certificate.image"
          :src="certificate.image"
          :alt="certificate.name"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div v-else class="w-full h-full flex items-center justify-center text-white">
          <Award class="w-8 h-8" />
        </div>

        <!-- Certificate Status Badge -->
        <div class="absolute top-1 left-1">
          <Badge
            :variant="certificateStatus.variant"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            {{ certificateStatus.label }}
          </Badge>
        </div>
      </div>

      <!-- Certificate Content -->
      <div class="flex-1 min-w-0">
        <!-- Certificate Title -->
        <h3 class="font-semibold text-slate-900 text-base mb-1 group-hover:text-amber-600 transition-colors line-clamp-1">
          {{ certificate.name }}
        </h3>

        <!-- Certificate Metadata -->
        <div class="flex items-center gap-4 text-xs text-slate-500 mb-2">
          <div class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>Obtained: {{ formatDatestamp(certificate.expiresTime) }}</span>
          </div>
        </div>

        <!-- Certificate Description or Additional Info -->
        <div class="text-sm text-slate-600 line-clamp-1">
          Certificate of completion
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex-shrink-0 flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          @click="handlePreview"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <ExternalLink class="w-4 h-4 mr-1" />
          Preview
        </Button>

        <Button
          v-if="certificate.expiresType === CertificateExpireTypeEnum.EXPIRED"
          variant="outline"
          size="sm"
          @click="handleRenew"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <RotateCcw class="w-4 h-4 mr-1" />
          Renew
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
