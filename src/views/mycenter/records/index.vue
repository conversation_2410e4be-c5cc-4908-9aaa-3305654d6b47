<script setup lang="ts" name="MyRecords">
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { ContainerScroll } from '@/components/ContainerWrap'
import { SmartPagination } from '@/components/SmartPagination'
import { ScrollArea } from '@/components/ui/scroll-area'
import TabNav from '@/components/TabNav.vue'
import RecordGrid from './components/RecordGrid.vue'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { FileText, Search, Filter, Calendar as CalendarIcon } from 'lucide-vue-next'

// Import APIs for different record types
import {
  MlcTrainingApi,
  TrainingStudyStatusEnum,
  ClassTypeEnum,
  type Options
} from '@/api/mycenter/myrecords/training'
import { InternalTrainingApi, InternalTrainingRespVO } from '@/api/mycenter/myrecords/internal'
import {
  ExternalTrainingReqTrainingApi,
  ExternalTrainingRespVO
} from '@/api/mycenter/myrecords/external'

interface RecordTab {
  key: string
  label: string
}

interface RecordItem {
  id: string
  name?: string
  title?: string
  type: string
  status?: any
  progress?: number
  score?: number
  duration?: number
  completedTime?: string
  category?: string
  description?: string
  cover?: string
}

// Record tabs configuration
const recordTabs: RecordTab[] = [
  { key: 'mlc-training', label: 'MLC Training' },
  { key: 'internal-training', label: 'Internal Training' },
  { key: 'external-training', label: 'External Training' }
]

// Reactive state
const selectedType = ref<string>('mlc-training')
const receivingCountryOptions = ref()
const costBearerOptions = ref()
const placeOptions = ref()
const companyOptions = ref()
const loading = ref(false)
const recordList = ref<RecordItem[]>([])
const total = ref(0)

const classStudyStatusOptions: Ref<Options[]> = ref([
  {
    value: TrainingStudyStatusEnum.BOOKED,
    label: 'Booked'
  },
  {
    value: TrainingStudyStatusEnum.PASSED,
    label: 'Passed '
  },
  {
    value: TrainingStudyStatusEnum.FAIL,
    label: 'Fail'
  },
  {
    value: TrainingStudyStatusEnum.POSTPONE,
    label: 'Postpone '
  },
  {
    value: TrainingStudyStatusEnum.REJECTED,
    label: 'Rejected'
  }
])

/** 课堂类型 */
const classTypes: Ref<Options[]> = ref([
  {
    value: ClassTypeEnum.OFFLINE_CLASS,
    label: 'Offline Class'
  },
  {
    value: ClassTypeEnum.VIRTUAL_CLASS,
    label: 'Virtual Class'
  },
  {
    value: ClassTypeEnum.HYBRID_CLASS,
    label: 'Hybrid Class'
  }
])

// Filter conditions for different training types
const mlcFilters = reactive({
  courseName: '',
  classType: '',
  status: '',
  startDate: null as Date | null,
  endDate: null as Date | null
})

const internalFilters = reactive({
  courseNameEN: '',
  courseNameAR: '',
  place: '',
  company: '',
  startDate: null as Date | null,
  endDate: null as Date | null
})

const externalFilters = reactive({
  courseNameEN: '',
  courseNameAR: '',
  country: '',
  costBearer: '',
  startDate: null as Date | null,
  endDate: null as Date | null
})

// Query parameters
const queryParams = reactive({
  pageNo: 1,
  pageSize: 12,
  courseName: '',
  title: '',
  trainerId: undefined,
  classRoomId: undefined,
  studyStatusList: [],
  type: undefined,
  startDate: '',
  endDate: '',
  queryType: undefined,
  place: undefined,
  implementingCompany: undefined,
  startTime: '',
  endTime: '',
  costBearer: '',
  receivingCountry: '',
  travelDate: '',
  returnDate: ''
})

// Handle tab change
const handleChangeType = async (type: string, resetPage = false) => {
  selectedType.value = type
  if (resetPage) {
    queryParams.pageNo = 1
  }
  // Reset filters when changing tabs
  resetFilters()

  // Load options data for the selected training type
  await getAllList()

  // Get record list for the selected type
  getRecordList()
}

// Handle filter change
const handleFilterChange = () => {
  console.log('Filter changed, current filters:', {
    selectedType: selectedType.value,
    mlcFilters: mlcFilters,
    internalFilters: internalFilters,
    externalFilters: externalFilters
  })
  queryParams.pageNo = 1
  getRecordList()
}

// Reset filters based on current tab
const resetFilters = () => {
  switch (selectedType.value) {
    case 'mlc-training':
      Object.assign(mlcFilters, {
        courseName: '',
        classType: '',
        status: '',
        startDate: null,
        endDate: null
      })
      break
    case 'internal-training':
      Object.assign(internalFilters, {
        courseNameEN: '',
        courseNameAR: '',
        place: '',
        company: '',
        startDate: null,
        endDate: null
      })
      break
    case 'external-training':
      Object.assign(externalFilters, {
        courseNameEN: '',
        courseNameAR: '',
        country: '',
        costBearer: '',
        startDate: null,
        endDate: null
      })
      break
  }
}

// Get record list based on selected type
const getRecordList = async () => {
  loading.value = true
  try {
    let response

    // Real API calls for different record types
    switch (selectedType.value) {
      case 'mlc-training':
        // Use MLC Training API with filters
        response = await MlcTrainingApi.getTrainingPage({
          pageNo: queryParams.pageNo,
          pageSize: queryParams.pageSize,
          courseName: mlcFilters.courseName || '',
          trainerId: 0,
          classRoomId: 0,
          studyStatusList: mlcFilters.status ? [Number(mlcFilters.status)] : [],
          type: mlcFilters.classType ? Number(mlcFilters.classType) : 0,
          startDate: formatDateForAPI(mlcFilters.startDate) || '',
          endDate: formatDateForAPI(mlcFilters.endDate) || '',
          queryType: 0
        })
        recordList.value =
          response.list?.map((item: any) => ({
            ...item,
            type: 'MlcTraining'
          })) || []
        total.value = response.total || 0
        break

      case 'internal-training':
        // Use Internal Training API with filters
        response = await InternalTrainingApi.getInternalTrainingPage({
          pageNo: queryParams.pageNo,
          pageSize: queryParams.pageSize,
          implementingCompany: internalFilters.company || '',
          startTime: formatDateForAPI(internalFilters.startDate) || '',
          endTime: formatDateForAPI(internalFilters.endDate) || '',
          title: internalFilters.courseNameEN || internalFilters.courseNameAR || ''
        })
        recordList.value =
          response.list?.map((item: any) => ({
            ...item,
            type: 'InternalTraining'
          })) || []
        total.value = response.total || 0
        break

      case 'external-training':
        // Use External Training API with filters
        response = await ExternalTrainingReqTrainingApi.getExternalTrainingPage({
          pageNo: queryParams.pageNo,
          pageSize: queryParams.pageSize,
          costBearer: externalFilters.costBearer || '',
          receivingCountry: externalFilters.country || '',
          travelDate: formatDateForAPI(externalFilters.startDate) || '',
          returnDate: formatDateForAPI(externalFilters.endDate) || '',
          title: externalFilters.courseNameEN || externalFilters.courseNameAR || ''
        })
        recordList.value =
          response.list?.map((item: any) => ({
            ...item,
            type: 'ExternalTraining'
          })) || []
        total.value = response.total || 0
        break

      default:
        recordList.value = []
        total.value = 0
    }
  } catch (error) {
    console.error('Failed to fetch record list:', error)
    recordList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取全部课程信息
const getAllList = async () => {
  console.log('Loading options for training type:', selectedType.value)
  try {
    switch (selectedType.value) {
      case 'external-training':
        console.log('Loading external training options...')
        await loadExternalTrainingOptions()
        break
      case 'internal-training':
        console.log('Loading internal training options...')
        await loadInternalTrainingOptions()
        break
      case 'mlc-training':
        // MLC Training 不需要额外的选项数据，因为选项是静态的
        console.log('MLC Training selected - using static options')
        break
      default:
        console.warn('Unknown training type:', selectedType.value)
    }
  } catch (error) {
    console.error('Error loading training options:', error)
  }
}

// 加载外部培训选项数据
const loadExternalTrainingOptions = async () => {
  const res = await ExternalTrainingReqTrainingApi.getExternalTrainingPage({
    pageNo: 1,
    pageSize: -1,
    costBearer: '',
    receivingCountry: '',
    travelDate: '',
    returnDate: '',
    title: ''
  })

  console.log('External training data loaded:', res.list?.length, 'items')

  // 提取并设置国家选项
  receivingCountryOptions.value = Array.from(
    new Set(res.list?.map((item: ExternalTrainingRespVO) => item.receivingCountry))
  )
    .filter(Boolean)
    .map((item) => ({ label: item, value: item }))

  // 提取并设置费用承担者选项
  costBearerOptions.value = Array.from(
    new Set(res.list?.map((item: ExternalTrainingRespVO) => item.costBearer))
  )
    .filter(Boolean)
    .map((item) => ({ label: item, value: item }))

  console.log('External training options loaded:', {
    countries: receivingCountryOptions.value.length,
    costBearers: costBearerOptions.value.length
  })
}

// 加载内部培训选项数据
const loadInternalTrainingOptions = async () => {
  const res = await InternalTrainingApi.getInternalTrainingPage({ pageNo: 1, pageSize: -1 })
  console.log('Internal training data loaded:', res.list?.length, 'items')

  // 提取并设置地点选项
  placeOptions.value = Array.from(
    new Set(res.list?.map((item: InternalTrainingRespVO) => item.place))
  )
    .filter(Boolean)
    .map((item) => ({ label: item, value: item }))
  companyOptions.value = Array.from(
    new Set(res.list?.map((item: InternalTrainingRespVO) => item.implementingCompany))
  )
    .filter(Boolean)
    .map((item) => ({ label: item, value: item }))

  console.log('Internal training options loaded:', {
    places: placeOptions.value.length,
    companies: companyOptions.value.length
  })
}

// Handle pagination
const handlePageChange = (page: number) => {
  queryParams.pageNo = page
  getRecordList()
}

// Handle record click
const handleRecordClick = (record: RecordItem) => {
  console.log('Record clicked:', record)
  // Add navigation logic here if needed
}

// Format date for display
const formatDate = (date: Date | string) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Format date for API (YYYY-MM-DD format)
const formatDateForAPI = (date: Date | string | null) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toISOString().split('T')[0]
}

// Computed property for current tab label
const currentTabLabel = computed(() => {
  const tab = recordTabs.find((t) => t.key === selectedType.value)
  return tab?.label || 'Records'
})

// Lifecycle
onMounted(() => {
  getRecordList()
  getAllList()
})
</script>

<template>
  <ContainerScroll :header-height="60">
    <!-- Header with Tabs -->
    <template #header>
      <div class="px-6 py-3 w-full h-full flex items-center">
        <TabNav
          :tabs="recordTabs"
          :active-tab="selectedType"
          @update:active-tab="($event: string) => handleChangeType($event, true)"
        />
      </div>
    </template>

    <!-- Filter Conditions -->
    <template #filter>
      <div class="px-6 py-4 bg-primary/2">
        <!-- MLC Training Filters -->
        <div v-if="selectedType === 'mlc-training'" class="flex items-center gap-4 flex-wrap">
          <SuperSearch
            v-model="mlcFilters.courseName"
            placeholder="Course Title"
            @input="handleFilterChange"
          />
          <Select v-model="mlcFilters.classType" @value-change="handleFilterChange">
            <SelectTrigger class="w-[180px]">
              <SelectValue placeholder="Class Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem
                  v-for="classType in classTypes"
                  :key="classType.value"
                  :value="classType.value"
                >
                  {{ classType.label }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <Select v-model="mlcFilters.status" @value-change="handleFilterChange">
            <SelectTrigger class="w-[180px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem
                  v-for="classStudyStatus in classStudyStatusOptions"
                  :key="classStudyStatus.value"
                  :value="classStudyStatus.value"
                >
                  {{ classStudyStatus.label }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <Popover>
            <PopoverTrigger as-child>
              <Button
                variant="outline"
                class="w-40 justify-start text-left font-normal"
                :class="{ 'text-muted-foreground': !mlcFilters.startDate }"
              >
                <CalendarIcon class="mr-2 h-4 w-4" />
                {{ mlcFilters.startDate ? formatDate(mlcFilters.startDate) : 'Start date' }}
              </Button>
            </PopoverTrigger>
            <PopoverContent class="w-auto p-0" align="start">
              <Calendar
                :model-value="mlcFilters.startDate"
                mode="single"
                @update:model-value="
                  (date) => {
                    mlcFilters.startDate = date ? date.toDate('UTC') : null
                    handleFilterChange()
                  }
                "
              />
            </PopoverContent>
          </Popover>
          <span class="text-gray-400">-</span>
          <Popover>
            <PopoverTrigger as-child>
              <Button
                variant="outline"
                class="w-40 justify-start text-left font-normal"
                :class="{ 'text-muted-foreground': !mlcFilters.endDate }"
              >
                <CalendarIcon class="mr-2 h-4 w-4" />
                {{ mlcFilters.endDate ? formatDate(mlcFilters.endDate) : 'End date' }}
              </Button>
            </PopoverTrigger>
            <PopoverContent class="w-auto p-0" align="start">
              <Calendar
                :model-value="mlcFilters.endDate"
                mode="single"
                @update:model-value="
                  (date) => {
                    mlcFilters.endDate = date ? date.toDate('UTC') : null
                    handleFilterChange()
                  }
                "
              />
            </PopoverContent>
          </Popover>
        </div>

        <!-- Internal Training Filters -->
        <div
          v-else-if="selectedType === 'internal-training'"
          class="flex items-center gap-4 flex-wrap"
        >
          <SuperSearch
            v-model="internalFilters.courseNameEN"
            placeholder="Course Title EN"
            @input="handleFilterChange"
          />
          <!-- <Input
            v-model="internalFilters.courseNameAR"
            placeholder="Course Title AR"
            class="w-48"
            @input="handleFilterChange"
          /> -->
          <Select v-model="internalFilters.place" @value-change="handleFilterChange">
            <SelectTrigger class="w-[180px]">
              <SelectValue placeholder="Place" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem v-for="place in placeOptions" :key="place.value" :value="place.value">
                  {{ place.label }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <Select v-model="internalFilters.company" @value-change="handleFilterChange">
            <SelectTrigger class="w-[180px]">
              <SelectValue placeholder="Company" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem
                  v-for="company in companyOptions"
                  :key="company.value"
                  :value="company.value"
                >
                  {{ company.label }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <Popover>
            <PopoverTrigger as-child>
              <Button
                variant="outline"
                class="w-40 justify-start text-left font-normal"
                :class="{ 'text-muted-foreground': !internalFilters.startDate }"
              >
                <CalendarIcon class="mr-2 h-4 w-4" />
                {{
                  internalFilters.startDate ? formatDate(internalFilters.startDate) : 'Start date'
                }}
              </Button>
            </PopoverTrigger>
            <PopoverContent class="w-auto p-0" align="start">
              <Calendar
                :model-value="internalFilters.startDate"
                mode="single"
                @update:model-value="
                  (date) => {
                    internalFilters.startDate = date ? date.toDate('UTC') : null
                    handleFilterChange()
                  }
                "
              />
            </PopoverContent>
          </Popover>
          <span class="text-gray-400">-</span>
          <Popover>
            <PopoverTrigger as-child>
              <Button
                variant="outline"
                class="w-40 justify-start text-left font-normal"
                :class="{ 'text-muted-foreground': !internalFilters.endDate }"
              >
                <CalendarIcon class="mr-2 h-4 w-4" />
                {{ internalFilters.endDate ? formatDate(internalFilters.endDate) : 'End date' }}
              </Button>
            </PopoverTrigger>
            <PopoverContent class="w-auto p-0" align="start">
              <Calendar
                :model-value="internalFilters.endDate"
                mode="single"
                @update:model-value="
                  (date) => {
                    internalFilters.endDate = date ? date.toDate('UTC') : null
                    handleFilterChange()
                  }
                "
              />
            </PopoverContent>
          </Popover>
        </div>

        <!-- External Training Filters -->
        <div
          v-else-if="selectedType === 'external-training'"
          class="flex items-center gap-4 flex-wrap"
        >
          <SuperSearch
            v-model="externalFilters.courseNameEN"
            placeholder="Course Title EN"
            @input="handleFilterChange"
          />
          <Select v-model="externalFilters.country" @value-change="handleFilterChange">
            <SelectTrigger class="w-[180px]">
              <SelectValue placeholder="Country" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem
                  v-for="receivingCountry in receivingCountryOptions"
                  :key="receivingCountry.value"
                  :value="receivingCountry.value"
                >
                  {{ receivingCountry.label }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <Select v-model="externalFilters.costBearer" @value-change="handleFilterChange">
            <SelectTrigger class="w-[180px]">
              <SelectValue placeholder="Cost Bearer" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem
                  v-for="costBearer in costBearerOptions"
                  :key="costBearer.value"
                  :value="costBearer.value"
                >
                  {{ costBearer.label }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <Popover>
            <PopoverTrigger as-child>
              <Button
                variant="outline"
                class="w-40 justify-start text-left font-normal"
                :class="{ 'text-muted-foreground': !externalFilters.startDate }"
              >
                <CalendarIcon class="mr-2 h-4 w-4" />
                {{
                  externalFilters.startDate ? formatDate(externalFilters.startDate) : 'Start date'
                }}
              </Button>
            </PopoverTrigger>
            <PopoverContent class="w-auto p-0" align="start">
              <Calendar
                :model-value="externalFilters.startDate"
                mode="single"
                @update:model-value="
                  (date) => {
                    externalFilters.startDate = date ? date.toDate('UTC') : null
                    handleFilterChange()
                  }
                "
              />
            </PopoverContent>
          </Popover>
          <span class="text-gray-400">-</span>
          <Popover>
            <PopoverTrigger as-child>
              <Button
                variant="outline"
                class="w-40 justify-start text-left font-normal"
                :class="{ 'text-muted-foreground': !externalFilters.endDate }"
              >
                <CalendarIcon class="mr-2 h-4 w-4" />
                {{ externalFilters.endDate ? formatDate(externalFilters.endDate) : 'End date' }}
              </Button>
            </PopoverTrigger>
            <PopoverContent class="w-auto p-0" align="start">
              <Calendar
                :model-value="externalFilters.endDate"
                mode="single"
                @update:model-value="
                  (date) => {
                    externalFilters.endDate = date ? date.toDate('UTC') : null
                    handleFilterChange()
                  }
                "
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </template>

    <!-- Scrollable Content -->
    <div class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="p-6">
          <RecordGrid
            :record-list="recordList"
            :loading="loading"
            :current-tab-label="currentTabLabel"
            @record-click="handleRecordClick"
          />
        </div>
      </ScrollArea>
    </div>

    <!-- Fixed Pagination at Bottom -->
    <template #footer v-if="total > 0">
      <div class="px-6 py-4 w-full h-full flex items-center">
        <SmartPagination
          :total="total"
          :current-page="queryParams.pageNo"
          :page-size="queryParams.pageSize"
          @current-change="handlePageChange"
        />
      </div>
    </template>
  </ContainerScroll>
</template>

<style scoped lang="scss"></style>
