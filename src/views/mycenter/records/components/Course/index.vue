<script setup lang="ts" name="RecordsCourse">
import { CourseApi, CourseItem, CourseStatusEnum } from '@/api/course/content'

interface Options {
  value: number
  label: string
}

const loading = ref(false)
const total = ref(0)
const courseList = ref<Array<CourseItem>>([])
const queryParams = reactive({
  name: undefined,
  pageNum: 1,
  pageSize: 10
})

const courseStatusOptions: Ref<Options[]> = [
  {
    label: 'Off Shelf',
    value: CourseStatusEnum.OFF_SHELF
  },
  {
    label: 'On Shelf',
    value: CourseStatusEnum.ON_SHELF
  }
]

// 获取课程信息
const getList = async () => {
  loading.value = true
  try {
    const res = await CourseApi.listCourse(queryParams)
    courseList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  queryParams.pageNum = 1
  getList()
}

const handleReset = () => {
  queryParams.name = undefined
  queryParams.pageNum = 1
  getList()
}

const handleCurrentChange = (newPage: number) => {
  queryParams.pageNum = newPage
  getList()
}

onMounted(() => {
  getList()
})
</script>

<template>
  <div class="pt-4 pb-4 flex items-center space-x-4">
    <div class="flex items-center space-x-2">
      <Label for="name">Title</Label>
      <Input
        id="name"
        placeholder="Please input"
        v-model="queryParams.name"
        clearable
        class="w-[320px]"
        @keydown.enter="handleSearch"
      />
    </div>
  </div>
  <div :loading="loading" v-if="courseList.length > 0">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead> Title </TableHead>
          <TableHead> Created Time </TableHead>
          <TableHead class="text-right"> Status </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow v-for="course in courseList" :key="course.id">
          <TableCell class="font-medium">
            {{ course.name }}
          </TableCell>
          <TableCell>{{ course.createTime }}</TableCell>
          <TableCell class="text-right">
            {{
              courseStatusOptions.find((courseStatus) => courseStatus.value === course?.status)
                ?.label
            }}
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  </div>
  <EmptyPlaceholder v-if="total === 0" title="No data" />
  <SmartPagination
    v-if="total > 0"
    :total="total"
    :current-page="queryParams.pageNum"
    :page-size="queryParams.pageSize"
    @current-change="handleCurrentChange"
  />
</template>

<style scoped lang="scss"></style>
