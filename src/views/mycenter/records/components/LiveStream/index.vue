<template>
  <div class="pt-4 pb-4 flex items-center space-x-4">
    <div class="flex items-center space-x-2">
      <Input
        id="name"
        placeholder="Please input"
        v-model="queryParams.name"
        clearable
        class="w-[180px]"
        @keydown.enter="handleSearch"
      />
    </div>
    <div class="flex items-center space-x-2">
      <Select v-model="queryParams.statusList" @update:model-value="handleSearch">
        <SelectTrigger class="w-[180px]">
          <SelectValue placeholder="Select status" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem
              v-for="liveStatus in liveStatusOptions"
              :key="liveStatus.value"
              :value="liveStatus.value"
            >
              {{ liveStatus.label }}
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
    <div class="flex items-center space-x-2">
      <DatePicker ref="startDateRef" @change="handleStartDateChange"></DatePicker>
      <span class="text-stone-400">-</span>
      <DatePicker ref="endDateRef" @change="handleEndDateChange"></DatePicker>
    </div>
  </div>

  <div :loading="loading" v-if="liveList.length > 0">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead> Live Stream Name </TableHead>
          <TableHead> Stream Trainer </TableHead>
          <TableHead> Start Time </TableHead>
          <TableHead> Status </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow v-for="item in liveList" :key="item.id">
          <TableCell>{{ item.name }}</TableCell>
          <TableCell>
            {{ item.speakers ? item.speakers?.map((item) => item.nickname).join(',') : '' }}
          </TableCell>
          <TableCell>{{ item.startTime }}</TableCell>
          <TableCell>{{ item.statusName }}</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  </div>
  <EmptyPlaceholder v-if="liveList.length === 0" title="No data" />
  <SmartPagination
    v-if="total > 0"
    :total="total"
    :current-page="queryParams.pageNo"
    :page-size="queryParams.pageSize"
    @current-change="handleCurrentChange"
  />
</template>
<script setup lang="ts" name="RecordsLiveStream">
import { LiveStreamApi, LiveRoomRespVO, LiveStatusEnum } from '@/api/mycenter/myrecords/livestream'
import DatePicker from '@/views/live/detail/components/DatePicker.vue'
import dayjs from 'dayjs'
import { DateValue, getLocalTimeZone } from '@internationalized/date'
import { useUserStore } from '@/store/modules/user'

interface Options {
  value: number
  label: string
}

const userStore = useUserStore()
const loading = ref(false)
const total = ref(0)
const liveList = ref<LiveRoomRespVO[]>([])
const queryParams = reactive({
  name: undefined,
  pageNo: 1,
  pageSize: 10,
  statusList: undefined,
  startTime: undefined,
  endTime: undefined,
  userId: userStore.user.id
})
const endDateRef = ref()
const startDateRef = ref()

const liveStatusOptions: Ref<Options[]> = ref([
  {
    label: 'To be released',
    value: LiveStatusEnum.TO_BE_RELEASED
  },
  {
    label: 'To be broadcast',
    value: LiveStatusEnum.TO_BE_BROADCAST
  },
  {
    label: 'Live broadcast',
    value: LiveStatusEnum.LIVE_BROADCAST
  },
  {
    label: 'Closed',
    value: LiveStatusEnum.CLOSED
  }
])
// 获取直播信息
const getList = async () => {
  loading.value = true
  try {
    const res = await LiveStreamApi.getMyLiveRoomPage(queryParams)
    liveList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

// 将DateValue类型的时间转为正常的Date类型
const handleCalenderToNormal = (date: DateValue) => {
  return date.toDate(getLocalTimeZone())
}
const formatDate = (date: Date | string | null) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}
const handleStartDateChange = (date: DateValue) => {
  if (date) {
    const curCheckedStartDate = handleCalenderToNormal(date)
    const formattedStartDate = formatDate(curCheckedStartDate)
    queryParams.startTime = `${formattedStartDate} 00:00:00`
  } else {
    queryParams.startTime = undefined
  }
  handleSearch()
}
const handleEndDateChange = (date: DateValue) => {
  if (date) {
    const curCheckedEndDate = handleCalenderToNormal(date)
    const formattedEndDate = formatDate(curCheckedEndDate)
    queryParams.endTime = `${formattedEndDate} 00:00:00`
  } else {
    queryParams.endTime = undefined
  }
  handleSearch()
}

const handleSearch = () => {
  queryParams.pageNo = 1
  getList()
}

const handleReset = () => {
  queryParams.pageNo = 1
  queryParams.name = undefined
  queryParams.statusList = undefined
  queryParams.startTime = undefined
  queryParams.endTime = undefined
  endDateRef.value.date = undefined
  startDateRef.value.date = undefined
  getList()
}

const handleCurrentChange = (newPage: number) => {
  queryParams.pageNo = newPage
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss"></style>
