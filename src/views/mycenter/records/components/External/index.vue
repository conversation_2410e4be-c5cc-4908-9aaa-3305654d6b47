<script setup lang="ts" name="RecordsExternalTraining">
import {
  ExternalTrainingReqTrainingApi,
  ExternalTrainingRespVO
} from '@/api/mycenter/myrecords/external'
import DatePicker from '@/views/live/detail/components/DatePicker.vue'
import dayjs from 'dayjs'
import { DateValue, getLocalTimeZone } from '@internationalized/date'
import { formatDateArray } from '@/utils/formatDate'

const loading = ref(false)
const total = ref(0)

const externalList = ref<Array<ExternalTrainingRespVO>>([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  titleAr: undefined,
  receivingCountry: undefined,
  costBearer: undefined,
  createTime: []
})
const endDateRef = ref()
const startDateRef = ref()
const receivingCountryOptions = ref()
const costBearerOptions = ref()
// 获取课程信息
const getList = async () => {
  loading.value = true
  try {
    const res = await ExternalTrainingReqTrainingApi.getExternalTrainingPage(queryParams)
    externalList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

// 将DateValue类型的时间转为正常的Date类型
const handleCalenderToNormal = (date: DateValue) => {
  return date.toDate(getLocalTimeZone())
}
const formatDate = (date: Date | string | null) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}
const handleStartDateChange = (date: DateValue) => {
  if (date) {
    const curCheckedStartDate = handleCalenderToNormal(date)
    const formattedStartDate = formatDate(curCheckedStartDate)
    queryParams.createTime[0] = `${formattedStartDate} 00:00:00`
  } else {
    queryParams.createTime = []
  }
  if (queryParams.createTime[0] && queryParams.createTime[1]) {
    handleSearch()
  }
}
const handleEndDateChange = (date: DateValue) => {
  if (date) {
    const curCheckedEndDate = handleCalenderToNormal(date)
    const formattedEndDate = formatDate(curCheckedEndDate)

    queryParams.createTime[1] = `${formattedEndDate} 23:59:59`
  } else {
    queryParams.createTime = []
  }
  if (queryParams.createTime[0] && queryParams.createTime[1]) {
    handleSearch()
  }
}

const handleSearch = () => {
  queryParams.pageNo = 1
  getList()
}

const handleReset = () => {
  queryParams.pageNo = 1
  queryParams.title = undefined
  queryParams.titleAr = undefined
  queryParams.costBearer = undefined
  queryParams.receivingCountry = undefined
  queryParams.createTime = []
  endDateRef.value.date = undefined
  startDateRef.value.date = undefined
  getList()
}
const getAllList = async () => {
  const res = await ExternalTrainingReqTrainingApi.getExternalTrainingPage({
    pageNo: 1,
    pageSize: -1
  })
  receivingCountryOptions.value = Array.from(
    new Set(res.list?.map((item: ExternalTrainingRespVO) => item.receivingCountry))
  )
    .filter(Boolean)
    .map((item) => ({ label: item, value: item }))
  costBearerOptions.value = Array.from(
    new Set(res.list?.map((item: ExternalTrainingRespVO) => item.costBearer))
  )
    .filter(Boolean)
    .map((item) => ({ label: item, value: item }))
}

const handleCurrentChange = (newPage: number) => {
  queryParams.pageNo = newPage
  getList()
}

onMounted(() => {
  getList()
  getAllList()
})
</script>
<template>
  <div class="pt-4 pb-4 flex items-center flex-wrap space-x-4">
    <div class="flex items-center space-x-2">
      <Input
        id="title"
        placeholder="Course Title EN"
        v-model="queryParams.title"
        clearable
        class="w-[140px]"
        @keydown.enter="handleSearch"
      />
    </div>
    <div class="flex items-center space-x-2">
      <Input
        id="titleAr"
        placeholder="Course Title AR"
        v-model="queryParams.titleAr"
        clearable
        class="w-[140px]"
        @keydown.enter="handleSearch"
      />
    </div>
    <div class="flex items-center space-x-2">
      <Select v-model="queryParams.receivingCountry" @update:model-value="handleSearch">
        <SelectTrigger class="w-[140px]">
          <SelectValue placeholder="Country" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem
              v-for="receivingCountry in receivingCountryOptions"
              :key="receivingCountry.value"
              :value="receivingCountry.value"
            >
              {{ receivingCountry.label }}
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
    <div class="flex items-center space-x-2">
      <Select v-model="queryParams.costBearer" @update:model-value="handleSearch">
        <SelectTrigger class="w-[140px]">
          <SelectValue placeholder="Cost bearer" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem
              v-for="costBearer in costBearerOptions"
              :key="costBearer.value"
              :value="costBearer.value"
            >
              {{ costBearer.label }}
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
    <div class="flex items-center space-x-2">
      <DatePicker ref="startDateRef" @change="handleStartDateChange"></DatePicker>
      <span class="text-stone-400">-</span>
      <DatePicker ref="endDateRef" @change="handleEndDateChange"></DatePicker>
    </div>
  </div>

  <div :loading="loading" v-if="externalList.length > 0">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead> Training Code </TableHead>
          <TableHead> Course Title EN </TableHead>
          <TableHead> Course Title AR </TableHead>
          <TableHead> The receiving Country </TableHead>
          <TableHead> Travel Date </TableHead>
          <TableHead> Return Date </TableHead>
          <TableHead> Admin No. </TableHead>
          <TableHead> Cost Bearer </TableHead>
          <!--          <TableHead> Student Number </TableHead>-->
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow v-for="external in externalList" :key="external.id">
          <TableCell>{{ external.code }}</TableCell>
          <TableCell>{{ external.title }}</TableCell>
          <TableCell>{{ external.titleAr }}</TableCell>
          <TableCell>{{ external.receivingCountry }}</TableCell>
          <TableCell>{{ external.travelDate }}</TableCell>
          <TableCell>{{ external.returnDate }}</TableCell>
          <TableCell>{{ external.adminNo }}</TableCell>
          <TableCell>{{ external.costBearer }}</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  </div>
  <EmptyPlaceholder v-if="total === 0" title="No data" />
  <SmartPagination
    v-if="total > 0"
    :total="total"
    :current-page="queryParams.pageNo"
    :page-size="queryParams.pageSize"
    @current-change="handleCurrentChange"
  />
</template>

<style scoped lang="scss"></style>
