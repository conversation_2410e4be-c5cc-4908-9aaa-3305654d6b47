<script setup lang="ts" name="RecordsInternalTraining">
import {
  InternalTrainingApi,
  InternalTrainingRespVO,
  InternalTypeEnum
} from '@/api/mycenter/myrecords/internal'
import DatePicker from '@/views/live/detail/components/DatePicker.vue'
import dayjs from 'dayjs'
import { DateValue, getLocalTimeZone } from '@internationalized/date'

interface Options {
  value: number
  label: string
}

const loading = ref(false)
const total = ref(0)
const internalList = ref<Array<InternalTrainingRespVO>>([])
const queryParams = reactive({
  title: undefined,
  titleAr: undefined,
  pageNo: 1,
  pageSize: 10,
  place: undefined,
  implementingCompany: undefined,
  createTime: []
})
const endDateRef = ref()
const startDateRef = ref()
const placeOptions = ref()
const companyOptions = ref()
// 获取课程信息
const getList = async () => {
  loading.value = true
  try {
    const res = await InternalTrainingApi.getInternalTrainingPage(queryParams)
    internalList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

// 将DateValue类型的时间转为正常的Date类型
const handleCalenderToNormal = (date: DateValue) => {
  return date.toDate(getLocalTimeZone())
}
const formatDate = (date: Date | string | null) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}
const handleStartDateChange = (date: DateValue) => {
  if (date) {
    const curCheckedStartDate = handleCalenderToNormal(date)
    const formattedStartDate = formatDate(curCheckedStartDate)
    queryParams.createTime[0] = `${formattedStartDate} 00:00:00`
  } else {
    queryParams.createTime = []
  }
  if (queryParams.createTime[0] && queryParams.createTime[1]) {
    handleSearch()
  }
}
const handleEndDateChange = (date: DateValue) => {
  if (date) {
    const curCheckedEndDate = handleCalenderToNormal(date)
    const formattedEndDate = formatDate(curCheckedEndDate)
    queryParams.createTime[1] = `${formattedEndDate} 23:59:59`
  } else {
    queryParams.createTime = []
  }
  if (queryParams.createTime[0] && queryParams.createTime[1]) {
    handleSearch()
  }
}

const getAllList = async () => {
  const res = await InternalTrainingApi.getInternalTrainingPage({ pageNo: 1, pageSize: -1 })
  placeOptions.value = Array.from(
    new Set(res.list?.map((item: InternalTrainingRespVO) => item.place))
  )
    .filter(Boolean)
    .map((item) => ({ label: item, value: item }))
  companyOptions.value = Array.from(
    new Set(res.list?.map((item: InternalTrainingRespVO) => item.implementingCompany))
  )
    .filter(Boolean)
    .map((item) => ({ label: item, value: item }))
}

const handleSearch = () => {
  queryParams.pageNo = 1
  getList()
}

const handleReset = () => {
  queryParams.pageNo = 1
  queryParams.title = undefined
  queryParams.titleAr = undefined
  queryParams.place = undefined
  queryParams.implementingCompany = undefined
  queryParams.createTime = []
  endDateRef.value.date = undefined
  startDateRef.value.date = undefined
  getList()
}

/** 国内课程类型 */
const internalTypes: Ref<Options[]> = [
  {
    label: 'Course',
    value: InternalTypeEnum.COURSE
  },
  {
    label: 'Training',
    value: InternalTypeEnum.TRAINING
  },
  {
    label: 'Visit',
    value: InternalTypeEnum.VISIT
  },
  {
    label: 'Conference',
    value: InternalTypeEnum.CONFERENCE
  },
  {
    label: 'Lecture',
    value: InternalTypeEnum.LECTURE
  },
  {
    label: 'Meeting',
    value: InternalTypeEnum.MEETING
  },
  {
    label: 'Workshop',
    value: InternalTypeEnum.WORKSHOP
  }
]

const handleCurrentChange = (newPage: number) => {
  queryParams.pageNo = newPage
  getList()
}

onMounted(() => {
  getList()
  getAllList()
})
</script>
<template>
  <div class="pt-4 pb-4 flex items-center flex-wrap space-x-4">
    <div class="flex items-center space-x-2">
      <Input
        id="title"
        placeholder="Course Title EN"
        v-model="queryParams.title"
        clearable
        class="w-[140px]"
        @keydown.enter="handleSearch"
      />
    </div>
    <div class="flex items-center space-x-2">
      <Input
        id="titleAr"
        placeholder="Course Title AR"
        v-model="queryParams.titleAr"
        clearable
        class="w-[140px]"
        @keydown.enter="handleSearch"
      />
    </div>
    <div class="flex items-center space-x-2">
      <Select v-model="queryParams.place" @update:model-value="handleSearch">
        <SelectTrigger class="w-[140px]">
          <SelectValue placeholder="Place" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem v-for="place in placeOptions" :key="place.value" :value="place.value">
              {{ place.label }}
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
    <div class="flex items-center space-x-2">
      <Select v-model="queryParams.implementingCompany" @update:model-value="handleSearch">
        <SelectTrigger class="w-[140px]">
          <SelectValue placeholder="Company" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem
              v-for="company in companyOptions"
              :key="company.value"
              :value="company.value"
            >
              {{ company.label }}
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
    <div class="flex items-center space-x-2">
      <DatePicker ref="startDateRef" @change="handleStartDateChange"></DatePicker>
      <span class="text-stone-400">-</span>
      <DatePicker ref="endDateRef" @change="handleEndDateChange"></DatePicker>
    </div>
  </div>

  <div :loading="loading" v-if="internalList.length > 0">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead> Training Code </TableHead>
          <TableHead> Course Title EN </TableHead>
          <TableHead> Course Title AR </TableHead>
          <TableHead> Type </TableHead>
          <TableHead> Place </TableHead>
          <TableHead> Start & End Date </TableHead>
          <TableHead> Company </TableHead>
          <TableHead> Course Duration </TableHead>
          <!--          <TableHead> Student Number </TableHead>-->
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow v-for="internal in internalList" :key="internal.id">
          <TableCell>{{ internal.title }}</TableCell>
          <TableCell>{{ internal.titleAr }}</TableCell>
          <TableCell>{{ internal.titleAr }}</TableCell>
          <TableCell>{{
            internalTypes.find((internalType) => internalType.value === internal?.type)?.label
          }}</TableCell>
          <TableCell>{{ internal.place }}</TableCell>
          <TableCell>{{ internal.startDate }} - {{ internal.endDate }}</TableCell>
          <TableCell>{{ internal.implementingCompany }}</TableCell>
          <TableCell>{{ internal.duration }} days</TableCell>
          <!--          <TableCell>{{ internal.userCount }}</TableCell>-->
        </TableRow>
      </TableBody>
    </Table>
  </div>
  <EmptyPlaceholder v-if="total === 0" title="No data" />
  <SmartPagination
    v-if="total > 0"
    :total="total"
    :current-page="queryParams.pageNo"
    :page-size="queryParams.pageSize"
    @current-change="handleCurrentChange"
  />
</template>

<style scoped lang="scss"></style>
