<script setup lang="ts">
import { TrainingApi } from '@/api/training/mlctraining'
import {
  ClassLanguageEnum,
  ClassStatusEnum,
  MlcTrainingApi,
  MlcTrainingRespVO,
  ClassBookingStatusEnum,
  ClassTypeEnum
} from '@/api/mycenter/myrecords/training'
import ProcessInstanceTimeline from '@/views/home/<USER>/components/todotraining/components/ProcessInstanceTimeline.vue'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import FeedBack from './FeedBack.vue'
import { formatDate } from '@/utils/formatTime'
import { useUserStore } from '@/store/modules/user'

interface Options {
  value: number
  label: string
}

const message = useMessage()
const userStore = useUserStore()
const courseInfo = ref() // 课程信息
const classInfo = ref() // 课堂信息
const isDialogOpen = ref(false)
const formLoading = ref(false)
const isFeedback = ref(false)
const feedBackRef = ref()
const isProcessInstance = ref(false) // 是否展示流程图
const classId = ref() // 记录课堂id
// 审批节点信息
const activityNodes = ref<ProcessInstanceApi.ApprovalNodeInfo[]>([])
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const courseList = ref()

/** 课堂类型 */
const classTypes: Ref<Options[]> = [
  {
    label: 'Offline Class',
    value: ClassTypeEnum.OFFLINE_CLASS
  },
  {
    label: 'Virtual Class',
    value: ClassTypeEnum.VIRTUAL_CLASS
  },
  {
    label: 'Hybrid Class',
    value: ClassTypeEnum.HYBRID_CLASS
  }
]

/** 课堂语言 */
const classLanguages: Ref<Options[]> = ref([
  {
    label: 'EN',
    value: ClassLanguageEnum.EN
  },
  {
    label: 'AR',
    value: ClassLanguageEnum.AR
  },
  {
    label: 'CN',
    value: ClassLanguageEnum.CN
  }
])

/** 课堂预定状态 */
const classBookingStatusOptions: Ref<Options[]> = ref([
  {
    label: 'Approving',
    value: ClassBookingStatusEnum.APPROVING
  },
  {
    label: 'Rejected',
    value: ClassBookingStatusEnum.Rejected
  },
  {
    label: 'Approved',
    value: ClassBookingStatusEnum.APPROVED
  }
])

/** 获取审批详情 */
const getApprovalDetail = async (processInstanceId: number) => {
  try {
    const param = {
      processInstanceId: processInstanceId,
      taskId: processInstanceId
    }
    const data = await ProcessInstanceApi.getApprovalDetail(param)
    // 获取审批节点，显示 Timeline 的数据
    activityNodes.value = data.activityNodes
  } catch {}
}

// 获取课程信息详情
const getCourseInfo = async (id: number) => {
  try {
    courseInfo.value = await TrainingApi.getTraining(id)
  } catch (e) {}
}

// 获取课堂详情信息
const getClassInfo = async (id: number) => {
  formLoading.value = false
  try {
    classInfo.value = await MlcTrainingApi.getClass(id)
    await getCourseInfo(classInfo.value?.courseId)
    // 检查课程是否学习完成
    courseList.value = await MlcTrainingApi.checkCourseComplete([
      courseInfo.value?.prerequisteCourse
    ])
    const res = await MlcTrainingApi.getClassBookingPage({
      pageNo: 1,
      pageSize: -1,
      classId: classInfo.value?.id,
      userIds: userStore.user.id
    })
    if (res?.list && res.list.length > 0) {
      isProcessInstance.value = true
      // 获得审批详情
      await getApprovalDetail(res.list[0]?.processInstanceId)
    }
  } finally {
    formLoading.value = false
  }
}

// 是否已经反馈过
const getClassFeedback = async (id: number) => {
  try {
    const data = await MlcTrainingApi.getClassFeedback(id)
    isFeedback.value = !data
  } catch (e) {}
}

const openDialog = async (item: MlcTrainingRespVO) => {
  // 校验该课程是否已分配 会出现的情况是该课堂的在管理端反馈二维码被放到了教室的大屏上，但是该课堂没有分给某个人，但是他也扫了码,那么此时他是没有权限进行反馈操作的
  classId.value = item.classId
  try {
    const data = await MlcTrainingApi.checkClassAssigned(item.classId)
    if (data) {
      isProcessInstance.value = false
      await getClassInfo(item.classId)
      // 是否反馈
      await getClassFeedback(item.classId)
      isDialogOpen.value = true
    } else {
      message.error('The course has not been assigned to you')
    }
  } finally {
  }
}

// 打开反馈弹框
const changeFeedBack = () => {
  feedBackRef.value.openDialog(classInfo.value?.id)
}
// 反馈成功后界面刷新是否展示反馈按钮
const handleConfirm = async () => {
  await getClassFeedback(classId.value)
}

defineExpose({
  openDialog
})
</script>

<template>
  <Dialog v-model:open="isDialogOpen">
    <DialogContent class="max-w-none w-[800px]" disableOutsidePointerEvents>
      <DialogHeader>
        <DialogTitle>Booking Information</DialogTitle>
      </DialogHeader>
      <div v-loading="formLoading">
        <div class="flex justify-between items-center">
          <div class="flex">
            <div class="flex items-center w-[200px] truncate">
              {{ courseInfo?.title }}
            </div>
            <div class="ms-[30px]">
              <Badge>{{
                classLanguages.find((classLanguage) => classLanguage.value === classInfo?.language)
                  ?.label
              }}</Badge>
              <Badge class="ms-2">{{
                classTypes.find((classType) => classType.value === classInfo?.type)?.label
              }}</Badge>
            </div>
          </div>
          <Badge class="ms-2">{{
            classBookingStatusOptions.find(
              (classBookingStatus) => classBookingStatus.value === classInfo?.status
            )?.label
          }}</Badge>
        </div>
        <div class="flex flex-col">
          <span>Training Description</span>
          <span class="text-[#BBBBBB] mt-2">
            {{ courseInfo?.remarks }}
          </span>
        </div>
        <div class="flex flex-col mt-3">
          <span>Start and End Time</span>
          <span class="text-[#BBBBBB] mt-2">
            {{
              classInfo?.startTime && classInfo?.endTime
                ? `${classInfo?.startDate} ${classInfo?.startTime} ~ ${classInfo?.startDate} ${classInfo?.endTime}`
                : ''
            }}
          </span>
        </div>
        <!--      feedBack-->
        <div
          v-show="
            (classInfo?.status === ClassStatusEnum.CANCELLED ||
              classInfo?.status === ClassStatusEnum.POSTPONED) &&
            !isFeedback
          "
        >
          <Button @click="changeFeedBack">
            <Icon name="Book"></Icon>
            Feedback
          </Button>
        </div>
        <span>Booking Information</span>
        <div class="flex">
          <span>Allowed Booking Time:</span>
          <span class="ms-3 text-[#F7C785]"
            >{{ formatDate(classInfo?.bookingStartTime) }} -
            {{ formatDate(classInfo?.bookingEndTime) }}</span
          >
        </div>
        <div class="flex">
          <span>Booking Rate:</span>
          <div class="flex ms-3">
            <span class="text-[#F7C785]">{{
              classInfo?.assignNum ? classInfo?.assignNum : 0
            }}</span>
            <span>/</span>
            <span>{{ classInfo?.maxNum }}</span>
          </div>
        </div>
        <div class="flex">
          <span>Prerequisite:</span>
          <div class="ms-3">
            <div>Course:</div>
            <div class="w-[500px] flex flex-wrap">
              <div
                class="flex w-[150px] h-[30px] me-3 m2-3"
                v-for="course in courseList"
                :key="course.courseId"
              >
                <div class="w-[150px] truncate">{{ course.courseName }}</div>
              </div>
            </div>
            <div>Attachment:</div>
            <div class="w-[500px] flex flex-wrap">
              <Badge
                v-for="attachment in courseInfo?.prerequisteAttachment?.split(',')"
                :key="attachment"
                class="me-2"
              >
                {{ attachment }}</Badge
              >
            </div>
          </div>
        </div>
        <!--        <div class="flex" v-show="isProcessInstance">-->
        <!--          <div> Review Process </div>-->
        <!--          <div class="ms-2">-->
        <!--            &lt;!&ndash; 审批记录时间线 &ndash;&gt;-->
        <!--            <ProcessInstanceTimeline :activity-nodes="activityNodes" />-->
        <!--          </div>-->
        <!--        </div>-->
      </div>
    </DialogContent>
  </Dialog>

  <FeedBack ref="feedBackRef" @success="handleConfirm" />
</template>

<style scoped lang="scss">
.acitve {
  @apply text-[#ffff]  rounded-3xl bg-linear-to-r from-[#0B61E9] to-[#8A71EC] pl-4 pr-4;
}
:deep .pagination-container {
  background: #edf6f2;
}
</style>
