<script setup lang="ts">
import { MlcTrainingApi } from '@/api/mycenter/myrecords/training'

import positiveImg from '@/assets/images/class/positive.svg'
import neturalImg from '@/assets/images/class/netural.svg'
import negativeImg from '@/assets/images/class/negative.svg'

const message = useMessage()
const loading = ref(false)
const isDialogOpen = ref(false)
const formData = ref({
  classId: undefined,
  courseEvaluation: undefined,
  trainerEvaluation: undefined,
  facilityEvaluation: undefined,
  comments: undefined
})

const icons = ref([
  { id: 1, src: positiveImg },
  { id: 2, src: neturalImg },
  { id: 3, src: negativeImg }
])

const resetFrom = () => {
  formData.value = {
    classId: undefined,
    courseEvaluation: undefined,
    trainerEvaluation: undefined,
    facilityEvaluation: undefined,
    comments: undefined
  }
}
const openDialog = (id: number) => {
  resetFrom()
  formData.value.classId = id
  isDialogOpen.value = true
}
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
// 保存反馈
const handleConfirm = async () => {
  loading.value = true
  try {
    await MlcTrainingApi.createClassFeedback(formData.value)
    message.success('Feedback Success')
    isDialogOpen.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openDialog
})
</script>

<template>
  <Dialog v-model:open="isDialogOpen">
    <DialogContent class="max-w-none w-[450px]" disableOutsidePointerEvents>
      <DialogHeader>
        <DialogTitle>Feedback</DialogTitle>
      </DialogHeader>
      <div class="flex" v-loading="loading">
        <div class="w-1/2">Training Course</div>
        <div class="flex ms-3 w-1/2">
          <div
            v-for="(icon, index) in icons"
            :key="index"
            class="w-[20px] h-[20px] me-5 last:me-0 cursor-pointer"
            @click="formData.courseEvaluation = icon.id"
          >
            <img
              :src="icon.src"
              class="w-full h-full object-contain"
              :class="{ selected: formData.courseEvaluation === icon.id }"
            />
          </div>
        </div>
      </div>
      <div class="flex">
        <div class="w-1/2">Trainer</div>
        <div class="flex ms-3 w-1/2">
          <div
            v-for="(icon, index) in icons"
            :key="index"
            class="w-[20px] h-[20px] me-5 last:me-0 cursor-pointer"
            @click="formData.trainerEvaluation = icon.id"
          >
            <img
              :src="icon.src"
              alt=""
              class="w-full h-full object-contain"
              :class="{ selected: formData.trainerEvaluation === icon.id }"
            />
          </div>
        </div>
      </div>
      <div class="flex">
        <div class="w-1/2">Training Course</div>
        <div class="flex ms-3 w-1/2">
          <div
            v-for="(icon, index) in icons"
            :key="index"
            class="w-[20px] h-[20px] me-5 last:me-0 cursor-pointer"
            @click="formData.facilityEvaluation = icon.id"
          >
            <img
              :src="icon.src"
              alt=""
              class="w-full h-full object-contain"
              :class="{ selected: formData.facilityEvaluation === icon.id }"
            />
          </div>
        </div>
      </div>
      <div>
        How Please tell us what you found good about this training,or mention aspects that could be
        improved.Feel free to suggest additions or topics to be removed.
      </div>
      <Textarea v-model="formData.comments" maxlength="2000" />
      <DialogFooter>
        <Button variant="outline" @click="isDialogOpen = false">Cancel</Button>
        <Button @click="handleConfirm">Confirm</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<style scoped lang="scss">
.selected {
  background-color: orange;
  border-radius: 15px;
}
</style>
