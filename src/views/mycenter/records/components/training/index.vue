<script setup lang="ts" name="RecordsMlcTraining">
import {
  ClassLanguageEnum,
  ClassStatusEnum,
  MlcTrainingApi,
  MlcTrainingRespVO,
  TrainingStudyStatusEnum,
  ClassTypeEnum
} from '@/api/mycenter/myrecords/training'
import DatePicker from '@/views/live/detail/components/DatePicker.vue'
import { DateValue, getLocalTimeZone } from '@internationalized/date'
import dayjs from 'dayjs'
import { isTimeBefore, formatTimestamp, formatTime } from '@/utils/formatTime'
import ClassInfo from './components/ClassInfo.vue'
import { QueryTypeEnum } from '@/api/training/mlctraining'

interface Options {
  value: number
  label: string
}

const loading = ref(false)
const classInfoRef = ref()
const total = ref(0)
const startDateRef = ref()
const endDateRef = ref()
const classList = ref<Array<MlcTrainingRespVO>>([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  courseName: undefined,
  studyStatusList: [],
  type: undefined,
  startDate: undefined,
  endDate: undefined,
  queryType: QueryTypeEnum.MY_SELF
})

const classStudyStatusOptions: Ref<Options[]> = ref([
  {
    value: TrainingStudyStatusEnum.BOOKED,
    label: 'Booked'
  },
  {
    value: TrainingStudyStatusEnum.PASSED,
    label: 'Passed '
  },
  {
    value: TrainingStudyStatusEnum.FAIL,
    label: 'Fail'
  },
  {
    value: TrainingStudyStatusEnum.POSTPONE,
    label: 'Postpone '
  },
  {
    value: TrainingStudyStatusEnum.REJECTED,
    label: 'Rejected'
  }
])

/** 课堂类型 */
const classTypes: Ref<Options[]> = ref([
  {
    value: ClassTypeEnum.OFFLINE_CLASS,
    label: 'Offline Class'
  },
  {
    value: ClassTypeEnum.VIRTUAL_CLASS,
    label: 'Virtual Class'
  },
  {
    value: ClassTypeEnum.HYBRID_CLASS,
    label: 'Hybrid Class'
  }
])

/** 课堂语言 */
const classLanguages: Ref<Options[]> = ref([
  {
    label: 'EN',
    value: ClassLanguageEnum.EN
  },
  {
    label: 'AR',
    value: ClassLanguageEnum.AR
  },
  {
    label: 'CN',
    value: ClassLanguageEnum.CN
  }
])

/** 获取课堂状态 */
const classStatusOptions: Ref<Options[]> = ref([
  {
    label: 'Draft',
    value: ClassStatusEnum.DRAFT
  },
  {
    label: 'Not Started',
    value: ClassStatusEnum.NOT_STARTED
  },
  {
    label: 'Ongoing',
    value: ClassStatusEnum.ONGOING
  },
  {
    label: 'Ended',
    value: ClassStatusEnum.ENDED
  },
  {
    label: 'Postponed',
    value: ClassStatusEnum.POSTPONED
  },
  {
    label: 'Cancelled',
    value: ClassStatusEnum.CANCELLED
  }
])

// 将DateValue类型的时间转为正常的Date类型
const handleCalenderToNormal = (date: DateValue) => {
  return date.toDate(getLocalTimeZone())
}
const formatDate = (date: Date | string | null) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}
const handleStartDateChange = (date: DateValue) => {
  if (date) {
    const curCheckedStartDate = handleCalenderToNormal(date)
    queryParams.startDate = formatDate(curCheckedStartDate)
  } else {
    queryParams.startDate = undefined
  }
  handleSearch()
}
const handleEndDateChange = (date: DateValue) => {
  if (date) {
    const curCheckedEndDate = handleCalenderToNormal(date)
    queryParams.endDate = formatDate(curCheckedEndDate)
  } else {
    queryParams.endDate = undefined
  }
  handleSearch()
}

// 获取Mlc课程信息
const getList = async () => {
  loading.value = true
  try {
    const res = await MlcTrainingApi.getTrainingPage(queryParams)
    classList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  queryParams.pageNo = 1
  getList()
}

const handleReset = () => {
  queryParams.courseName = undefined
  queryParams.studyStatusList = []
  queryParams.startDate = undefined
  queryParams.endDate = undefined
  queryParams.type = undefined
  queryParams.pageNo = 1
  endDateRef.value.date = undefined
  startDateRef.value.date = undefined
  getList()
}

const handleCurrentChange = (newPage: number) => {
  queryParams.pageNo = newPage
  getList()
}

const handleDetail = (item: MlcTrainingRespVO) => {
  classInfoRef.value.openDialog(item)
}

onMounted(() => {
  getList()
})
</script>

<template>
  <div class="pt-4 pb-4 flex items-center flex-wrap space-x-3">
    <div class="flex items-center space-x-1">
      <Input
        id="name"
        placeholder="Course Title"
        v-model="queryParams.courseName"
        clearable
        class="w-[140px]"
        @keydown.enter="handleSearch"
      />
    </div>
    <div class="flex items-center space-x-1">
      <Select v-model="queryParams.type" @update:model-value="handleSearch">
        <SelectTrigger class="w-[140px]">
          <SelectValue placeholder="Class Type" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem
              v-for="classType in classTypes"
              :key="classType.value"
              :value="classType.value"
            >
              {{ classType.label }}
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
    <div class="flex items-center space-x-1">
      <Select v-model="queryParams.studyStatusList" @update:model-value="handleSearch">
        <SelectTrigger class="w-[140px]">
          <SelectValue placeholder="Status" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem
              v-for="classStudyStatus in classStudyStatusOptions"
              :key="classStudyStatus.value"
              :value="classStudyStatus.value"
            >
              {{ classStudyStatus.label }}
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
    <div class="flex items-center space-x-1">
      <DatePicker ref="startDateRef" @change="handleStartDateChange"></DatePicker>
      <span class="text-stone-400">-</span>
      <DatePicker ref="endDateRef" @change="handleEndDateChange"></DatePicker>
    </div>
  </div>
  <div :loading="loading" v-if="classList">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Course Title </TableHead>
          <!--          <TableHead class="w-[100px]"> Class Code </TableHead>-->
          <TableHead> Class Type </TableHead>
          <!--          <TableHead class="w-[100px]"> Trainer </TableHead>-->
          <TableHead> Language </TableHead>
          <TableHead> Classroom </TableHead>
          <!--          <TableHead class="w-[100px]"> Start Time </TableHead>-->
          <!--          <TableHead class="w-[100px]"> End Time </TableHead>-->
          <!--          <TableHead class="w-[100px]"> Validity </TableHead>-->
          <TableHead> Status </TableHead>
          <TableHead> Duration </TableHead>
          <!--          <TableHead class="w-[200px]"> Check-out Time </TableHead>-->
          <TableHead class="text-right"> Action </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow v-for="clazz in classList" :key="clazz.id">
          <TableCell>{{ clazz.courseName }}</TableCell>
          <!--          <TableCell>{{ clazz.code }}</TableCell>-->
          <TableCell>{{
            classTypes.find((classType) => classType.value === clazz.type)?.label
          }}</TableCell>
          <!--          <TableCell>{{ clazz.trainerName }}</TableCell>-->
          <TableCell>{{
            classLanguages.find((classLanguage) => classLanguage.value === clazz?.language)?.label
          }}</TableCell>
          <TableCell>{{ clazz.classRoomName }}</TableCell>
          <!--          <TableCell>{{ clazz.startTime }}</TableCell>-->
          <!--          <TableCell>{{ clazz.endTime }}</TableCell>-->
          <!--          <TableCell>{{ clazz.validity }}</TableCell>-->
          <TableCell>{{
            classStatusOptions.find((classStatus) => classStatus.value === clazz?.status)?.label
          }}</TableCell>
          <TableCell>{{ clazz.startTime }} - {{ clazz.endTime }}</TableCell>
          <!--          <TableCell>{{ clazz.endTime }}</TableCell>-->
          <TableCell class="text-right">
            <Button @click="handleDetail(clazz)"> Detail </Button>
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  </div>
  <EmptyPlaceholder v-if="total === 0 || total === null" title="No data" />

  <SmartPagination
    v-if="total > 0"
    :total="total"
    :current-page="queryParams.pageNo"
    :page-size="queryParams.pageSize"
    @current-change="handleCurrentChange"
  />
  <ClassInfo ref="classInfoRef" />
</template>

<style scoped lang="scss"></style>