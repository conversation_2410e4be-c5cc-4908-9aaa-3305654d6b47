<script setup lang="ts">
const router = useRouter()
const props = defineProps({
  update: Function,
  data: {
    type: Object,
    default: () => ({})
  }
})
const handleClick = () => {
  router.push({
    name: 'OnboardingDetail',
    params: { id: props.data.id },
    query: { isMandatory: props.data.isMandatory }
  })
}
</script>

<template>
  <div class="cursor-pointer" @click="handleClick">
    <div class="relative overflow-hidden rounded-md cursor-pointer">
      <LazyImage
        :src="props.data.cover"
        :alt="props.data?.name"
        :aspect-ratio="'square'"
        class="w-[330x]"
      />
      <div class="w-[280px] line-clamp-1 text-ellipsis my-5">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger as-child>
              <span class="text-lg font-bold text-black line-clamp-1">
                {{ props.data.title }}
              </span>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <div>
                {{ props.data.title }}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
