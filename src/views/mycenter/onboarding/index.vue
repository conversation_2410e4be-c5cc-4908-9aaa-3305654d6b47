<script setup lang="ts" name="Contents">
import {
  categoryList,
  onboardMyList,
  OnboardingNavListVO,
  OnboardingNavReqVO
} from '@/api/onboarding'
import Onboard from './components/Onboard.vue'

const courseList = ref<Array<OnboardingNavListVO>>([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  title: undefined,
  categoryId: undefined
})
const total = ref(0)
const loading = ref(false)
const categoryData = ref<{ id: number; title: string; sort: number }[]>([])
const nodataShow = ref(false)
const getCategoryList = async () => {
  categoryData.value = await categoryList('')
}
const getOnboardList = async () => {
  courseList.value = []
  const params = {
    ...queryParams.value
  }
  loading.value = true
  try {
    const data = await onboardMyList(params as OnboardingNavReqVO)
    if (data && data.list && data.list.length > 0) {
      courseList.value = data.list
      total.value = data.total
    } else {
      nodataShow.value = true
    }
  } finally {
    loading.value = false
  }
}
const handleSearch = () => {
  queryParams.value.pageNum = 1
  queryParams.value.pageSize = 20
  nodataShow.value = false
  getOnboardList()
}
const handleReset = () => {
  queryParams.value.pageSize = 20
  queryParams.value.pageNum = 1
  queryParams.value.categoryId = undefined
  queryParams.value.title = ''
  handleSearch()
}
onMounted(() => {
  getOnboardList()
  getCategoryList()
})
const handleCurrentChange = (newPage: number) => {
  queryParams.value.pageNum = newPage
  getOnboardList()
}
</script>

<template>
  <!-- onboarding 查询表单 -->
  <div class="flex pt-5 space-x-4">
    <div class="flex items-center space-x-2">
      <label class="me-2">Title</label>
      <Input
        v-model="queryParams.title"
        placeholder="Please input"
        class="w-[320px]"
        clearable
        @keydown.enter="handleSearch"
      />
      <label class="mx-2">Category</label>
      <Select v-model="queryParams.categoryId">
        <SelectTrigger class="w-[320px]">
          <SelectValue placeholder="Select" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem
              v-for="item in categoryData"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            >
            </SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
      <div class="space-x-2">
        <Button @click="handleSearch"> Search </Button>
        <Button @click="handleReset" variant="outline"> Reset </Button>
      </div>
    </div>
  </div>
  <!-- Course List -->
  <div v-loading="loading" class="p-30 min-h-[480px]">
    <div class="grid grid-cols-4 gap-6 mb-4">
      <div v-for="(item, index) in courseList" :key="`${item.updateBy}_${index}`">
        <Onboard :data="item" />
      </div>
    </div>
    <div class="flex justify-center" v-show="courseList.length == 0">
      <EmptyPlaceholder />
    </div>
    <SmartPagination
      v-show="total > 0"
      :current-page="queryParams.pageNum"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss"></style>
