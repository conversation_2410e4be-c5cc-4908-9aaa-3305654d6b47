<script setup lang="ts" name="MyCenter">
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useRouteQuery } from '@vueuse/router'
import MyCourses from './course/index.vue'
import MyOnboarding from './onboarding/index.vue'
import MyCompany from './policy/index.vue'
import MyExam from './exam/index.vue'
import MyRecords from './records/index.vue'
import MyLiveStreams from './livestreams/index.vue'
import MyCertificate from './certificate/index.vue'
import MySurvey from './survey/index.vue'
import { useUserStore } from '@/store/modules/user'
import userPlaceholder from '@/assets/images/user.png'
import { cn } from '@/lib/utils'
import {
  BookOpen,
  FileText,
  Users,
  Shield,
  FolderOpen,
  ClipboardList,
  Award,
  CheckCircle,
  User,
  GraduationCap,
  Search,
  Filter,
  Calendar,
  TrendingUp,
  Clock,
  Target
} from 'lucide-vue-next'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Input } from '@/components/ui/input'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import ContainerWrapper from '@/components/ContainerWrap/src/ContainerWrapper.vue'
import ContainerNav from '@/components/ContainerWrap/src/ContainerNav.vue'
import { ContainerScroll } from '@/components/ContainerWrap'
import { SuperSearch } from '@/components/SuperSearch'
import { SmartPagination } from '@/components/SmartPagination'
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()

// Use VueUse's useRouteQuery to handle route parameters
const activeTab = useRouteQuery('tab')

// Learning center menu configuration
const learningMenuList = ref([
  {
    title: 'My Courses',
    label: '',
    icon: BookOpen,
    variant: 'default',
    key: 'courses',
    description: 'View and manage your enrolled courses',
    count: 0
  },
  {
    title: 'My Exams',
    label: '',
    icon: FileText,
    variant: 'ghost',
    key: 'exams',
    description: 'Track your exam results and schedules',
    count: 0
  },
  {
    title: 'Company Policy',
    label: '',
    icon: Shield,
    variant: 'ghost',
    key: 'policy',
    description: 'Review company policies and compliance',
    count: 0
  },
  {
    title: 'My Training',
    label: '',
    icon: FolderOpen,
    variant: 'ghost',
    key: 'training',
    description: 'View your training history and progress',
    count: 0
  },
  {
    title: 'My Live Streams',
    label: '',
    icon: GraduationCap,
    variant: 'ghost',
    key: 'livestreams',
    description: 'View your live stream subscriptions and history',
    count: 0
  },
  {
    title: 'My Surveys',
    label: '',
    icon: ClipboardList,
    variant: 'ghost',
    key: 'surveys',
    description: 'Participate in surveys and view results',
    count: 0
  },
  {
    title: 'Certificates',
    label: '',
    icon: Award,
    variant: 'ghost',
    key: 'certificates',
    description: 'Download and manage your certificates',
    count: 0
  }
])

// 当前激活的菜单 - 基于路由参数
const activeLearningMenu = computed(() => {
  return (
    learningMenuList.value.find((item) => item.key === activeTab.value) || learningMenuList.value[0]
  )
})

// Current user information
const currentUser = computed(() => userStore.user)

// Handle pagination updates from child components
function handlePaginationUpdate(data: { total: number; currentPage: number; pageSize: number }) {
  // This can be used for global pagination state if needed
  console.log('Pagination updated:', data)
}

// Get current component based on active tab
const getCurrentComponent = computed(() => {
  switch (activeTab.value) {
    case 'courses':
      return MyCourses
    case 'exams':
      return MyExam
    // case 'onboarding':
    //   return MyOnboarding
    case 'policy':
      return MyCompany
    case 'training':
      return MyRecords
    case 'livestreams':
      return MyLiveStreams
    case 'certificates':
      return MyCertificate
    case 'surveys':
      return MySurvey
    default:
      return MyCourses
  }
})

// Handle menu click
function handleMenuClick(key: string) {
  activeTab.value = key

  // 为不同的tab设置默认参数
  const query: Record<string, string> = { tab: key }

  if (key === 'courses') {
    // 课程页面默认参数
    query.type = 'mandatory'
    query.status = 'all'
  }

  router.push({ query })
}

// 初始化默认路由参数
onMounted(() => {
  // 如果访问 my-center 没有任何参数，设置默认参数
  if (!activeTab.value) {
    router.replace({
      query: {
        tab: 'courses',
        type: 'mandatory',
        status: 'all'
      }
    })
  }
})

// Watch for route parameter changes and update menu state
watch(
  activeTab,
  (newTab) => {
    learningMenuList.value.forEach((item) => {
      item.variant = item.key === newTab ? 'default' : 'ghost'
    })
  },
  { immediate: true }
)
</script>

<template>
  <ContainerWrapper :nav-width="320">
    <template #nav>
      <div class="flex flex-col h-full">
        <!-- Enhanced User Profile Section -->
        <div
          class="p-8 border-b border-slate-200 dark:border-slate-700 bg-gradient-to-br from-slate-50 to-white dark:from-slate-800 dark:to-slate-900"
        >
          <div class="flex flex-col items-center space-y-6">
            <!-- User Avatar with Status -->
            <div class="relative">
              <div class="w-24 h-24 rounded-full bg-gradient-to-br from-[#017B3D] to-white p-1">
                <Avatar class="w-full h-full">
                  <AvatarImage
                    :src="currentUser?.avatar ? currentUser?.avatar : userPlaceholder"
                    alt="Profile picture"
                    class="object-cover"
                  />
                  <AvatarFallback class="text-xl bg-primary/10">
                    <User class="w-8 h-8" />
                  </AvatarFallback>
                </Avatar>
              </div>
              <!-- <div
                class="absolute -bottom-1 -right-1 w-7 h-7 bg-green-500 rounded-full border-3 border-white flex items-center justify-center"
              >
                <CheckCircle class="w-4 h-4 text-white" />
              </div> -->
            </div>

            <!-- User Info -->
            <div class="text-center w-full">
              <h2 class="text-xl font-bold text-slate-900 dark:text-white mb-1">{{
                currentUser.nickname || 'John Doe'
              }}</h2>
<!--              <p-->
<!--                class="text-sm text-slate-600 dark:text-slate-400 flex items-center justify-center gap-2 mb-3"-->
<!--              >-->
<!--                <span class="w-2 h-2 bg-primary rounded-full"></span>-->
<!--                {{-->
<!--                  currentUser || 'John Doe'-->
<!--                }}-->
<!--              </p>-->
<!--              <div class="flex items-center justify-center gap-2">-->
<!--                <Badge variant="secondary" class="text-xs px-3 py-1">Expert Certified</Badge>-->
<!--              </div>-->
            </div>
          </div>
        </div>

        <!-- Navigation Menu -->
        <ScrollArea class="flex-1">
          <div class="py-6">
            <nav class="space-y-2 px-4 pr-8">
              <div
                v-for="item in learningMenuList"
                :key="item.key"
                @click="handleMenuClick(item.key)"
                :class="
                  cn(
                    'relative flex items-center gap-3 px-4 py-3 cursor-pointer transition-all duration-200 group',
                    activeTab === item.key
                      ? 'nav-item-active'
                      : 'nav-item-hover text-slate-600 dark:text-slate-400 rounded-lg'
                  )
                "
                style="min-height: 48px"
              >
                <component :is="item.icon" class="w-5 h-5 flex-shrink-0 z-10" />
                <span class="flex-1 text-left text-sm font-medium z-10">{{ item.title }}</span>
                <Badge v-if="item.count > 0" variant="secondary" class="text-xs z-10">
                  {{ item.count }}
                </Badge>
              </div>
            </nav>
          </div>
        </ScrollArea>
      </div>
    </template>

    <template #content>
      <!-- Dynamic Component Rendering -->
      <component :is="getCurrentComponent" @update-pagination="handlePaginationUpdate" />
    </template>
  </ContainerWrapper>
</template>

<style lang="scss">
.tooltip-mywidth {
  max-width: 240px;
}

/* 激活菜单的箭头形状样式 - 恢复正确的箭头 */
.nav-item-active {
  position: relative;
  background: #017B3D;
  color: #fff !important;
  border-radius: 8px 0 0 8px;

  /* 创建右侧箭头 - 使用clip-path实现精确形状 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: -16px;
    width: 16px;
    height: 100%;
    background: #017B3D;
    clip-path: polygon(0 0, 100% 50%, 0 100%);
  }

  /* 确保内容颜色为白色 */
  * {
    color: #fff !important;
  }

  /* Badge 在激活状态下的样式 */
  .badge {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
  }
}

/* 未激活菜单的hover箭头效果 */
.nav-item-hover {
  position: relative;

  /* 预先创建箭头，但设为透明 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: -16px;
    width: 16px;
    height: 100%;
    background: transparent;
    clip-path: polygon(0 0, 100% 50%, 0 100%);
    transition: background-color 0.2s ease;
  }

  &:hover {
    background: #f1f5f9;
    color: #334155 !important;
    border-radius: 8px 0 0 8px;

    /* hover时显示灰色箭头 */
    &::after {
      background: #f1f5f9;
    }

    /* hover时的文字颜色 */
    * {
      color: #334155 !important;
    }
  }
}

/* 深色模式适配 */
.dark .nav-item-active {
  background: #017B3D;

  &::after {
    background: #017B3D;
  }
}

.dark .nav-item-hover {
  /* 深色模式下预先创建透明箭头 */
  &::after {
    background: transparent;
    transition: background-color 0.2s ease;
  }

  &:hover {
    background: #334155;
    color: #f1f5f9 !important;

    &::after {
      background: #334155;
    }

    * {
      color: #f1f5f9 !important;
    }
  }
}
</style>
