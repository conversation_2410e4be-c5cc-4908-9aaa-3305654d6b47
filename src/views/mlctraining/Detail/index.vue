<script setup lang="ts" name="TrainingDetail">
// import NotAdd from './components/NotAdd.vue'
import {
  PublishStatusEnum,
  QueryTypeEnum,
  TimeTypeEnum,
  TrainingApi,
  TrainingRespVO
} from '@/api/training/mlctraining'
import { getMonthStartAndEnd, getWeekStartAndEnd, getYearStartAndEnd } from '@/utils/formatDate'
import courseDefaultImg from '@/assets/images/training/courseDefaultImage.png'
import AddWaiting from '../components/AddWaiting.vue'
import ClassInfo from '../components/ClassInfo.vue'
import {
  ClassLanguageEnum,
  ClassStatusEnum,
  MlcTrainingApi,
  MlcTrainingRespVO,
  ClassBookingStatusEnum,
  ClassTypeEnum,
  ExistBookingEnum
} from '@/api/mycenter/myrecords/training'
import { LiveStatusEnum, RoomDetailVO } from '@/api/live/stream'
import { formatTimestampToDateAndTime } from '@/utils/formatTime'
import BasicForm from '@/views/live/detail/components/BasicForm.vue'
import { Check, Copy, MoreVertical, Trash2 } from 'lucide-vue-next'
import Participants from '@/views/live/detail/components/Participants.vue'
import Courseware from '@/views/live/detail/components/Courseware.vue'
import Setting from '@/views/live/detail/components/Setting.vue'
import Record from '@/views/live/detail/components/Record.vue'
const route = useRoute()
const training = ref<TrainingRespVO>()
const loading = ref(true)
const message = useMessage()

const templateSelection = ref<string[]>([])
const checkClass = ref() // 单独选择的课堂
interface Options {
  value: number
  label: string
}
const timeTypes: Ref<Options[]> = [
  {
    value: TimeTypeEnum.THIS_WEEK,
    label: 'This Week'
  },
  {
    value: TimeTypeEnum.THIS_MONTH,
    label: 'This Month'
  },
  {
    value: TimeTypeEnum.THIS_YEAR,
    label: 'This Year'
  }
]
const selectedTimeTypeValue = ref<TimeTypeEnum>(TimeTypeEnum.THIS_WEEK)

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  courseId: route.query.id,
  startDate: undefined,
  endDate: undefined,
  queryType: QueryTypeEnum.COURSE_ID,
  publishStatus: PublishStatusEnum.PUBLISHED // 状态(0.未发布 1.发布)
})
const emit = defineEmits(['close'])
const classList = ref() // 课堂信息
const total = ref(0)
const addWaitingRef = ref()
const classInfoRef = ref()

/** 课堂类型 */
const classTypes: Ref<Options[]> = ref([
  {
    label: 'Offline Class',
    value: ClassTypeEnum.OFFLINE_CLASS
  },
  {
    label: 'Virtual Class',
    value: ClassTypeEnum.VIRTUAL_CLASS
  },
  {
    label: 'Hybrid Class',
    value: ClassTypeEnum.HYBRID_CLASS
  }
])

/** 课堂语言 */
const classLanguageOptions: Ref<Options[]> = ref([
  {
    label: 'EN',
    value: ClassLanguageEnum.EN
  },
  {
    label: 'AR',
    value: ClassLanguageEnum.AR
  },
  {
    label: 'CN',
    value: ClassLanguageEnum.CN
  }
])

/** 课堂状态 */
const classStatusOptions: Ref<Options[]> = ref([
  {
    label: 'Draft',
    value: ClassStatusEnum.DRAFT
  },
  {
    label: 'Not Started',
    value: ClassStatusEnum.NOT_STARTED
  },
  {
    label: 'Ongoing',
    value: ClassStatusEnum.ONGOING
  },
  {
    label: 'Ended',
    value: ClassStatusEnum.ENDED
  },
  {
    label: 'Postponed',
    value: ClassStatusEnum.POSTPONED
  },
  {
    label: 'Cancelled',
    value: ClassStatusEnum.CANCELLED
  }
])

/** 课堂预定状态 */
const classBookingStatusOptions: Ref<Options[]> = ref([
  {
    label: 'Approving',
    value: ClassBookingStatusEnum.APPROVING
  },
  {
    label: 'Rejected',
    value: ClassBookingStatusEnum.Rejected
  },
  {
    label: 'Approved',
    value: ClassBookingStatusEnum.APPROVED
  },
  {
    label: 'Abnormal',
    value: ClassBookingStatusEnum.ABNORMAL
  },
  {
    label: 'Cancelled',
    value: ClassBookingStatusEnum.CANCELLED
  }
])

/**
 * 获取课程详细信息
 */
const getDetail = async (id: number) => {
  loading.value = true
  try {
    training.value = await TrainingApi.getTraining(id)
  } finally {
    loading.value = false
  }
}

/**
 * 获取课程下的课堂信息
 */
const getClassList = async () => {
  try {
    loading.value = true
    // 每次刷新清除已选中课堂数据
    templateSelection.value = []
    checkClass.value = []
    const res = await MlcTrainingApi.getTrainingPage(queryParams)
    classList.value = res.list
    total.value = res.total
  } catch (e) {
  } finally {
    loading.value = false
  }
}

const handleCurrentChange = (newPage: number) => {
  queryParams.pageNo = newPage
  getClassList()
}

const handleChangeSelect = (timeTypeValue: TimeTypeEnum) => {
  switch (timeTypeValue) {
    case TimeTypeEnum.THIS_WEEK:
      queryParams.startDate = getWeekStartAndEnd(1)[0]!
      queryParams.endDate = getWeekStartAndEnd(1)[1]!
      break
    case TimeTypeEnum.THIS_MONTH:
      queryParams.startDate = getMonthStartAndEnd(1)[0]!
      queryParams.endDate = getMonthStartAndEnd(1)[1]!
      break
    case TimeTypeEnum.THIS_YEAR:
      queryParams.startDate = getYearStartAndEnd(1)[0]!
      queryParams.endDate = getYearStartAndEnd(1)[1]!
      break
    default:
      queryParams.startDate = getMonthStartAndEnd(1)[0]!
      queryParams.endDate = getMonthStartAndEnd(1)[1]!
  }

  selectedTimeTypeValue.value = timeTypeValue

  getClassList()
}
const changeClass = (currentRow: MlcTrainingRespVO) => {
  checkClass.value = currentRow
}
const addWaiting = () => {
  addWaitingRef.value.openDialog(checkClass.value)
}

const handleBook = async (item: MlcTrainingRespVO) => {
  const data = await MlcTrainingApi.checkClassBooking(item.classId)
  // 再加一层校验是否允许跳转预定
  if (data) {
    classInfoRef.value.openDialog(item)
  } else {
    message.info('Reservation not allowed')
  }
}

onMounted(() => {
  if (route.query.id) {
    getDetail(route.query.id as unknown as number)
    // 默认查当天的月份的开始时间和结束时间
    queryParams.startDate = getMonthStartAndEnd(1)[0]!
    queryParams.endDate = getMonthStartAndEnd(1)[1]!
    getClassList(queryParams)
  }
})
</script>

<template>

  <ContainerWrapper>
    <template #content>
      <div class="flex h-full flex-col">
        <div class="flex items-center p-2 h-[52px]">
          <Breadcrumb/>
          <!--日期选择-->
          <div class="ms-auto flex justify-between items-center space-x-2">
            <div class="flex items-center space-x-2">
              <LinkItem
                v-for="timeType in timeTypes"
                :key="timeType.value"
                :item="{ name: timeType.label }"
                :active="timeType.value === selectedTimeTypeValue"
                @click="handleChangeSelect(timeType.value)"
              />
            </div>
          </div>

          <Separator orientation="vertical" class="mx-2" />
          <Button @click="addWaiting" :disabled="!checkClass?.classId"> Add to Waiting list </Button>
          <Separator orientation="vertical" class="mx-2" />
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="ghost" size="icon" :disabled="!message">
                <MoreVertical class="size-4" />
                <span class="sr-only">More</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Mark as unread</DropdownMenuItem>
              <DropdownMenuItem>Star thread</DropdownMenuItem>
              <DropdownMenuItem>Add label</DropdownMenuItem>
              <DropdownMenuItem>Mute thread</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <Separator />
        <ScrollArea class="flex-1 p-4">
          <div class="flex items-center space-x-2 mb-4">
            <!-- 左侧图片 -->
            <img :src="training?.cover || courseDefaultImg" class="h-30 rounded-lg" />
            <div class="flex-1 overflow-hidden">
              <div class="flex">
                <!-- 中间课程名称 -->
                <div class="flex-1">
                  <div class="flex items-center">
                    <span class="text-lg font-bold text-olpblack">{{ training?.title }}</span>
                  </div>
                </div>
              </div>
              <p class="text-muted-foreground">{{ training && training.remarks }}</p>
            </div>
          </div>

          <div v-loading="loading" v-if="classList" class="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="w-[100px]"> </TableHead>
                  <TableHead class="w-[100px]"> Course Title </TableHead>
                  <TableHead> Class Code </TableHead>
                  <TableHead> Class Type </TableHead>
                  <TableHead> Trainer </TableHead>
                  <TableHead> Language </TableHead>
                  <TableHead> Classroom </TableHead>
                  <TableHead> Date </TableHead>
                  <TableHead> Duration </TableHead>
                  <TableHead> Status </TableHead>
                  <TableHead class="text-right"> Action </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="clazz in classList" :key="clazz.id">
                  <TableCell>
                    <RadioGroup
                      v-model="templateSelection[0]"
                      class="flex gap-4"
                      @update:model-value="changeClass"
                    >
                      <RadioGroupItem :value="clazz" />
                    </RadioGroup>
                  </TableCell>

                  <TableCell class="font-medium">
                    {{ clazz.courseName }}
                  </TableCell>
                  <TableCell>{{ clazz.code }}</TableCell>
                  <TableCell>{{
                      classTypes.find((classType) => classType.value === clazz?.type)?.label
                    }}</TableCell>
                  <TableCell>{{ clazz.trainerName }}</TableCell>
                  <TableCell>{{
                      classLanguageOptions.find((classLanguage) => classLanguage.value === clazz?.language)
                        ?.label
                    }}</TableCell>
                  <TableCell>{{ clazz.classRoomName }}</TableCell>
                  <TableCell>{{ clazz.startDate }}</TableCell>
                  <TableCell>{{ clazz.startTime }} - {{ clazz.endTime }}</TableCell>
                  <TableCell
                  ><Badge variant="outline">{{
                      classStatusOptions.find((classStatus) => classStatus.value === clazz?.status)?.label
                    }}</Badge></TableCell
                  >
                  <TableCell class="text-right">
                    <Button v-if="clazz.existBooking === ExistBookingEnum.YES" @click="handleBook(clazz)">
                      Book
                    </Button>
                    <Button v-else>
                      {{
                        classBookingStatusOptions.find(
                          (classBookingStatus) => classBookingStatus.value === clazz?.bookingStatus
                        )?.label
                      }}
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
          <EmptyPlaceholder v-if="total === 0" />
        </ScrollArea>
        <div class="px-4 h-[52px] flex flex-col justify-center  border-t">
          <SmartPagination
            v-if="total > 0"
            :total="total"
            :current-page="queryParams.pageNo"
            :page-size="queryParams.pageSize"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </template>
  </ContainerWrapper>
  <AddWaiting ref="addWaitingRef" @success="getClassList" />
  <!--  Book操作-->
  <ClassInfo ref="classInfoRef" @success="getClassList" />


  <!-- 顶部课程简介 -->
<!--  <ContainerWrap title="Course Detail">-->
<!--    <template #action>-->
<!--      <div class="flex items-center space-x-2">-->
<!--        &lt;!&ndash; 左侧图片 &ndash;&gt;-->
<!--        <img :src="training?.cover || courseDefaultImg" class="h-30 rounded-lg" />-->
<!--        <div class="flex-1 overflow-hidden">-->
<!--          <div class="flex">-->
<!--            &lt;!&ndash; 中间课程名称 &ndash;&gt;-->
<!--            <div class="flex-1">-->
<!--              <div class="flex items-center">-->
<!--                <span class="text-lg font-bold text-olpblack">{{ training?.title }}</span>-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
<!--          <p class="text-muted-foreground">{{ training && training.remarks }}</p>-->
<!--        </div>-->
<!--      </div>-->
<!--      -->

<!--    </template>-->

<!--    -->
<!--    <SmartPagination-->
<!--      v-if="total > 0"-->
<!--      :total="total"-->
<!--      :current-page="queryParams.pageNo"-->
<!--      :page-size="queryParams.pageSize"-->
<!--      @current-change="handleCurrentChange"-->
<!--      class="mt-4"-->
<!--    />-->
<!--  </ContainerWrap>-->

<!--  <AddWaiting ref="addWaitingRef" @success="getClassList" />-->

<!--  &lt;!&ndash;  Book操作&ndash;&gt;-->
<!--  <ClassInfo ref="classInfoRef" @success="getClassList" />-->
</template>

<style scoped lang="scss"></style>
