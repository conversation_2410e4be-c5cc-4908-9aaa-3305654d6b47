<script setup lang="ts" name="AddWaiting">
import { FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form'
import DatePicker from '@/views/live/detail/components/DatePicker.vue'
import {
  MlcTrainingApi,
  MlcTrainingRespVO,
  TrainingLanguageEnum,
  WaitingTypeEnum
} from '@/api/mycenter/myrecords/training'
import { useUserStore } from '@/store/modules/user'
import { DeptAndUserMixedVO, RoomUserVO } from '@/api/live/stream'
import UserPicker from './UserPicker.vue'
import { DateValue, getLocalTimeZone } from '@internationalized/date'
import dayjs from 'dayjs'
import {
  getOneHourFifteenMinutesLater,
  get15Minutes,
  getTodayDate,
  isFutureDate,
  isTimeBefore,
  isTimeBeforeCurrent,
  formatLiveDate2,
  formatTime,
  compareTime,
  toCalendarDate,
  getTomorrowDate
} from '@/utils/formatTime'

interface DepartmentAndUserData extends DeptAndUserMixedVO {
  isChecked: boolean
}
interface Options {
  key: number
  label: string
}
const message = useMessage()
const userStore = useUserStore()
const loading = ref(false)
const isDialogOpen = ref(false)
const startDateRef = ref()
const endDateRef = ref()
const isSupplierHolder = ref(userStore.isSupplier) // 是否供应商
const UserPickerRef = ref()
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const speakers = ref<RoomUserVO[]>([])
const invitedUsers = ref<RoomUserVO[]>([])
const disabledUserList = ref<DeptAndUserMixedVO[]>([])
const formData = ref({
  id: undefined,
  classId: undefined,
  courseId: undefined,
  language: TrainingLanguageEnum.EN,
  translator: TrainingLanguageEnum.EN,
  userIds: [userStore.user.id],
  // 预定类型(1.自己预定 2.他人代订)
  type: WaitingTypeEnum.ADD_MYSELF,
  startPreferredDate: undefined,
  endPreferredDate: undefined
})
const userList = ref() // 用户信息
const waitingOptions: Ref<Options[]> = ref([
  {
    label: 'Add myself to waiting list',
    key: WaitingTypeEnum.ADD_MYSELF
  },
  {
    label: 'Add others to waiting list',
    key: WaitingTypeEnum.ADD_OTHER
  }
])

const translatorLanguageOptions: Ref<Options[]> = ref([
  {
    label: 'No',
    key: TrainingLanguageEnum.No
  },
  {
    label: 'EN',
    key: TrainingLanguageEnum.EN
  },
  {
    label: 'AR',
    key: TrainingLanguageEnum.AR
  },
  {
    label: 'CN',
    key: TrainingLanguageEnum.CN
  }
])
const userIds = ref([])
const classInfo = ref() // 课堂信息

const handleWaitingChange = (waitingTypeValue: WaitingTypeEnum) => {
  if (waitingTypeValue === WaitingTypeEnum.ADD_MYSELF) {
    userList.value = []
    formData.value.userIds = [userStore.user.id]
  }
}

// 将DateValue类型的时间转为正常的Date类型
const handleCalenderToNormal = (date: DateValue) => {
  return date.toDate(getLocalTimeZone())
}
const formatDate = (date: Date | string | null) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}

const startDate = ref()
const endDate = ref()

const handleStartDateChange = (date: DateValue) => {
  const curCheckedStartDate = handleCalenderToNormal(date)
  const curEndDate = endDateRef.value.date ? handleCalenderToNormal(endDateRef.value.date) : ''
  // 判断开始时间是否晚于当前时间
  const isRightDate = isFutureDate(curCheckedStartDate)
  if (!isRightDate) {
    // 不可小于当前时间
    message.warning('Start time cannot be earlier than current time')
    startDateRef.value.date = startDate.value
    return
  }
  // 选择的开始时间晚于结束时间，自动将结束时间设置为开始时间
  if (dayjs(curCheckedStartDate).isAfter(dayjs(curEndDate))) {
    endDateRef.value.date = date
    endDate.value = date

    const formattedStartDate = formatLiveDate2(curCheckedStartDate)
    formData.value.startPreferredDate = formattedStartDate
    formData.value.endPreferredDate = formattedStartDate
  } else {
    startDate.value = date

    // 更新liveBasicForm中的startTime
    const formattedStartDate = formatLiveDate2(curCheckedStartDate)
    formData.value.startPreferredDate = `${formattedStartDate}`
  }
}
const handleEndDateChange = (date: DateValue) => {
  // 选择的结束时间早于当日，报错
  const curCheckedEndDate = handleCalenderToNormal(date)
  const curStartDate = startDateRef.value.date
    ? handleCalenderToNormal(startDateRef.value.date)
    : ''
  const isRightDate = isFutureDate(curCheckedEndDate)
  if (!isRightDate) {
    // 不可小于当前时间
    message.warning('End time cannot be earlier than current time')
    endDateRef.value.date = endDate.value
    return
  }
  const formateStartDate = formatLiveDate2(curStartDate)
  const realStartDate = formateStartDate
  const formateEndDate = formatLiveDate2(curCheckedEndDate)
  const realEndDate = formateEndDate
  // 选择的结束时间早于开始时间（忽略时间部分），给出提示
  if (dayjs(realEndDate).isBefore(dayjs(realStartDate))) {
    message.warning('End time cannot be earlier than start time')
    // 恢复为之前的值
    endDateRef.value.date = endDate.value
    return
  } else {
    endDate.value = date
    formData.value.endPreferredDate = realEndDate
  }
}

const handleUserToDeptUserMixed = (users: RoomUserVO[]): DeptAndUserMixedVO[] => {
  return users.map((item: RoomUserVO) => {
    return {
      id: item.userId,
      name: item?.nickname,
      avatar: item?.avatar,
      isChecked: true,
      type: 2
    }
  })
}

// 打开选择用户弹框
const addInvitee = () => {
  UserPickerRef.value.openDialog(classInfo.value.id)
  UserPickerRef.value.selectedUsers = handleUserToDeptUserMixed(invitedUsers.value)
  disabledUserList.value = handleUserToDeptUserMixed(speakers.value)
}

const resetFrom = () => {
  formData.value = {
    language: TrainingLanguageEnum.EN,
    translator: TrainingLanguageEnum.EN,
    // 预定类型(1.自己预定 2.他人代订)
    type: WaitingTypeEnum.ADD_MYSELF,
    userIds: [userStore.user.id],
    startPreferredDate: undefined,
    endPreferredDate: undefined
  }
}

// 获取课程id
const getClassInfo = async (id: number) => {
  const data = await MlcTrainingApi.getTraining(id)
  formData.value.courseId = data.courseId
}
const openDialog = (data: MlcTrainingRespVO) => {
  // 清除列表选择用户信息
  userList.value = []
  userIds.value = []
  resetFrom()
  classInfo.value = data
  formData.value.language = data.language
  formData.value.classId = data.classId
  // 通过课堂id获取课程id
  getClassInfo(data.classId)
  // 获取课堂下的人员
  // getClassUserList(data.classId)
  isDialogOpen.value = true
}

// 获取课堂下的人员进行数据回显使用 暂时弃用 勿删!!!!
const getClassUserList = async (classId: number) => {
  const form = {
    pageNo: 1,
    pageSize: -1,
    classId
  }
  const data = await MlcTrainingApi.getClassUserPage(form)
  // 默认将系统用户id添加
  formData.value.userIds = data?.list
    ?.map((user: RoomUserVO) => user.userId)
    .concat([userStore.user.id])
  userIds.value = data?.list.map((user: RoomUserVO) => user.userId)
}

// 确定选择好的用户
const handleUserConfirm = (data: DepartmentAndUserData[]) => {
  userList.value = data
  // 为他人进行预定课堂
  const userIds = data.map((user: DepartmentAndUserData) => user.id)
  formData.value.userIds = userIds
}
// 保存
const handleConfirm = async () => {
  loading.value = true
  try {
    await MlcTrainingApi.createWaitingList(formData.value)
    isDialogOpen.value = false
    // 发送操作成功的事件
    emit('success')
    message.success('Added successfully')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openDialog
})
</script>

<template>
  <Dialog v-model:open="isDialogOpen">
    <DialogContent class="max-w-none w-[800px]" disableOutsidePointerEvents>
      <DialogHeader>
        <DialogTitle>Add Waiting List</DialogTitle>
      </DialogHeader>
      <div class="mt-4 space-y-4" v-loading="loading">
        <FormField name="">
          <FormItem>
            <!--            如果不是供应商管理员不显示一下内容-->
            <div class="flex" v-if="isSupplierHolder">
              <RadioGroup
                v-model="formData.type"
                @update:model-value="handleWaitingChange"
                class="flex gap-2"
              >
                <div
                  v-for="(waiting, index) in waitingOptions"
                  :key="index"
                  class="flex items-center gap-1"
                >
                  <RadioGroupItem :value="waiting.key" />
                  <Label>{{ waiting.label }}</Label>
                </div>
              </RadioGroup>
            </div>
            <Button
              @click="addInvitee"
              v-show="formData.type === WaitingTypeEnum.ADD_OTHER && isSupplierHolder"
              >Add</Button
            >
          </FormItem>
        </FormField>
        <FormField name="Student Information">
          <FormItem>
            <FormLabel>Student Information</FormLabel>
            <FormControl>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead class="w-[100px]"> Name </TableHead>
                    <TableHead> Badge Number </TableHead>
                    <TableHead> Company </TableHead>
                    <TableHead> Position </TableHead>
                    <TableHead class="text-right"> Full/Refresher </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-for="user in userList" :key="user.id">
                    <TableCell class="font-medium">
                      {{ user.name }}
                    </TableCell>
                    <TableCell>{{ user.badgeNumber }}</TableCell>
                    <TableCell>{{ user.companyName }}</TableCell>
                    <TableCell>{{ user.postName }}</TableCell>
                    <TableCell>{{ user.isFirstStudy }}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </FormControl>
          </FormItem>
        </FormField>
        <FormField name="Date">
          <FormItem>
            <FormLabel>Confirm preferred date</FormLabel>
            <FormControl>
              <div class="flex">
                <div class="flex-row items-center justify-between flex-1 gap-2 space-x-2">
                  <DatePicker ref="startDateRef" @change="handleStartDateChange"></DatePicker>
                  <span class="text-stone-400">-</span>
                  <DatePicker ref="endDateRef" @change="handleEndDateChange"></DatePicker>
                </div>
              </div>
            </FormControl>
          </FormItem>
        </FormField>
        <FormField name="Translator">
          <FormItem>
            <FormLabel>Translator</FormLabel>
            <FormControl>
              <div class="flex">
                <RadioGroup v-model="formData.translator" class="flex gap-4">
                  <div
                    v-for="(translatorLanguage, index) in translatorLanguageOptions"
                    :key="index"
                    class="flex items-center gap-1"
                  >
                    <RadioGroupItem :value="translatorLanguage.key" />
                    <Label>{{ translatorLanguage.label }}</Label>
                  </div>
                </RadioGroup>
              </div>
            </FormControl>
          </FormItem>
        </FormField>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="isDialogOpen = false">Cancel</Button>
        <Button @click="handleConfirm">Confirm</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <UserPicker
    ref="UserPickerRef"
    :disabledUserList="disabledUserList"
    @confirm="handleUserConfirm"
  />
</template>

<style scoped lang="scss">
.acitve {
  @apply text-[#ffff]  rounded-3xl bg-linear-to-r from-[#0B61E9] to-[#8A71EC] pl-4 pr-4;
}
:deep .pagination-container {
  background: #edf6f2;
}
</style>
