<script setup lang="ts">
import CourseDefault from '@/assets/images/training/courseDefaultImage.png'

const props = defineProps({
  update: Function,
  item: {
    type: Object,
    default: () => ({})
  }
})

</script>

<template>
  <Card class="cursor-pointer hover:shadow-lg transition-shadow duration-200">
    <CardContent class="p-0">
      <!-- 课程封面 -->
      <div class="relative overflow-hidden rounded-t-lg">
        <LazyImage
          :src="props.item.cover ? props.item.cover : CourseDefault"
          :aspect-ratio="'square'"
          class="w-full"
        />
      </div>

      <!-- 课程信息 -->
      <div class="p-4 space-y-3">
        <!-- 标题 -->
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger as-child>
              <CardTitle class="text-lg font-bold text-foreground line-clamp-2 leading-tight">
                {{ props.item.title }}
              </CardTitle>
            </TooltipTrigger>
            <TooltipContent side="bottom" class="max-w-xs">
              <p>{{ props.item.title }}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <!-- 有效期 -->
        <div class="flex items-center gap-2 text-sm text-muted-foreground">
          <Icon name="Timer" :size="14" />
          <span>{{ props.item.validity }} Month</span>
        </div>

        <!-- 课程类别 -->
        <div class="text-sm text-primary truncate" :title="props.item.categoryFullPath">
          {{ props.item.categoryFullPath }}
        </div>

        <!-- 描述 -->
        <CardDescription class="text-sm text-muted-foreground line-clamp-2 min-h-[2.5rem]">
          {{ props.item.remarks }}
        </CardDescription>
      </div>
    </CardContent>
  </Card>
</template>
