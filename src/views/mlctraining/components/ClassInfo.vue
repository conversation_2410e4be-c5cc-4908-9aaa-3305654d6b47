<script setup lang="ts">
import { BookingType<PERSON>num, TrainingApi } from '@/api/training/mlctraining'
import {
  Upload as UploadIcon,
  CheckCircle as CheckCircleIcon,
  Trash as TrashIcon,
  Eye as EyeIcon,
  Download as DownloadIcon,
  Calendar,
  Users,
  BookOpen,
  CheckCircle,
  XCircle,
  Paperclip,
  FileText
} from 'lucide-vue-next'
import { Card } from '@/components/ui/card'
import {
  ClassBookingSaveVO,
  MlcTrainingApi,
  MlcTrainingRespVO,
  ClassBookingStatusEnum,
  TrainingLanguageEnum,
  ClassTypeEnum,
  WaitingTypeEnum,
  CheckCourseCompleteRespVO
} from '@/api/mycenter/myrecords/training'
import UserPicker from './UserPicker.vue'
import { DeptAndUserMixedVO } from '@/api/live/stream'
import { SuperUpload } from '@/components/SuperUpload'
import { FileInfo, UploadResult } from '@/components/SuperUpload/src/config'
import { checkFile } from '@/utils/fileUtil'
import { nanoid } from 'nanoid'
import download from '@/utils/download'
import { formatDate } from '@/utils/formatTime'
import { useUserStore } from '@/store/modules/user'
import { b } from 'vite/dist/node/moduleRunnerTransport.d-DJ_mE5sf'

interface DepartmentAndUserData extends DeptAndUserMixedVO {
  isChecked: boolean
}

const userStore = useUserStore()
const message = useMessage()
const formLoading = ref(false)
const courseInfo = ref() // 课程信息
const classInfo = ref() // 课堂信息
const isDialogOpen = ref(false)
const UserPickerRef = ref()
const disabledUserList = ref<DeptAndUserMixedVO[]>([])
const bookCheck = ref(false)
// 限制的文件格式
const fileTypes = ['PPT', 'PPTX', 'DOC', 'DOCX', 'XLS', 'XLSX', 'PDF', 'JPG', 'JPEG', 'JFIF', 'PNG']
const fileInfoList = ref<FileInfo[]>([])
const fileList = ref<UploadResult[]>([])
const courseList = ref() // 课程信息列表
const attachmentList = ref() // 附件信息列表
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const formData = ref({
  courseId: undefined,
  classId: undefined,
  userId: '',
  courseJson: '',
  attachmentJson: '',
  type: WaitingTypeEnum.ADD_MYSELF
})
const fileInputRef = ref<HTMLInputElement | null>(null)
const userList = ref() // 用户信息
const userIds = ref([])
interface Options {
  value: number
  label: string
}
const bookingTypeOptions: Ref<Options[]> = ref([
  {
    value: BookingTypeEnum.BOOKING_FOR_MY_SELF,
    label: 'Book for myself'
  },
  {
    value: BookingTypeEnum.BOOKING_FOR_MY_OTHER,
    label: 'Booking for others'
  }
])
const date = ref()

/** 课堂类型 */
const classTypes: Ref<Options[]> = ref([
  {
    label: 'Offline Class',
    value: ClassTypeEnum.OFFLINE_CLASS
  },
  {
    label: 'Virtual Class',
    value: ClassTypeEnum.VIRTUAL_CLASS
  },
  {
    label: 'Hybrid Class',
    value: ClassTypeEnum.HYBRID_CLASS
  }
])

/** 课堂语言 */
const classLanguageOptions: Ref<Options[]> = ref([
  {
    label: 'EN',
    value: TrainingLanguageEnum.EN
  },
  {
    label: 'AR',
    value: TrainingLanguageEnum.AR
  },
  {
    label: 'CN',
    value: TrainingLanguageEnum.CN
  }
])

/** 课堂预定状态 */
const classBookingStatusOptions: Ref<Options[]> = ref([
  {
    label: 'Approving',
    value: ClassBookingStatusEnum.APPROVING
  },
  {
    label: 'Rejected',
    value: ClassBookingStatusEnum.Rejected
  },
  {
    label: 'Approved',
    value: ClassBookingStatusEnum.APPROVED
  }
])

// 打开选择用户弹框
const addInvitee = () => {
  UserPickerRef.value.openDialog(classInfo.value?.id)
}

// 获取课堂下的人员进行数据回显使用
// const getClassUserList = async (classId: number) => {
//   const form = {
//     pageNo: 1,
//     pageSize: -1,
//     classId
//   }
//   const data = await MlcTrainingApi.getClassUserPage(form)
//   formData.value.userId = data?.list
//     ?.map((user: RoomUserVO) => user.userId)
//     .concat([userStore.user.id])
//   userIds.value = data?.list.map((user: RoomUserVO) => user.userId).join(',')
// }
// 确定选择好的用户
const handleUserConfirm = (data: DepartmentAndUserData[]) => {
  userList.value = data
  formData.value.userId = data.id
}

// 获取课程信息详情
const getCourseInfo = async (id: number) => {
  courseInfo.value = await TrainingApi.getTraining(id)
  if (courseInfo.value?.prerequisteAttachment) {
    // 将字符串格式的转换数组 用于界面遍历渲染
    formData.value.attachmentJson = courseInfo.value?.prerequisteAttachment
      ?.split(',')
      ?.map((attachmentName) => {
        return {
          name: attachmentName
        }
      })
    attachmentList.value = formData.value.attachmentJson
  }
}

// 获取课堂信息详情
const getClassInfo = async (id: number) => {
  try {
    classInfo.value = await MlcTrainingApi.getClass(id)
    formData.value.courseId = classInfo.value.courseId
    await getCourseInfo(formData.value.courseId)
    date.value = formatDate(classInfo?.createTime).split(' ')[0]
    // 检查课程是否学习完成
    courseList.value = await MlcTrainingApi.checkCourseComplete([
      courseInfo.value?.prerequisteCourse
    ])
    if (courseList.value) {
      formData.value.courseJson = courseList.value?.map((course: CheckCourseCompleteRespVO) => {
        return {
          courseName: course.courseName,
          existStudy: course.existStudy
        }
      })
    }
  } catch (error) {}
}

const selectBookingType = (bookingTypeValue: BookingTypeEnum) => {
  if (bookingTypeValue === BookingTypeEnum.BOOKING_FOR_MY_SELF) {
    userList.value = []
    formData.value.userId = userStore.user.id
  } else {
    formData.value.userId = undefined
  }
}

const resetFrom = () => {
  formData.value = {
    type: WaitingTypeEnum.ADD_MYSELF,
    classId: undefined,
    courseId: undefined,
    courseJson: '',
    attachmentJson: ''
  }
  // 默认当前系统用户id
  formData.value.userId = userStore.user.id
}

const openDialog = (item: MlcTrainingRespVO) => {
  bookCheck.value = false
  // 清除文件信息
  fileInfoList.value = []
  fileList.value = []
  // 清除用户信息
  userList.value = []
  userIds.value = []
  resetFrom()
  formData.value.classId = item.classId
  getClassInfo(item.classId)
  // 获取课堂下的人员
  // getClassUserList(item.classId)
  isDialogOpen.value = true
}

// 手动触发文件选择框
const triggerFileSelect = () => {
  fileInputRef.value?.click()
}
// 监听文件选择
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files && files.length > 0) {
    const fileList = Array.from(files)
    fileList.forEach((file) => {
      uploadFile(file)
    })
  }
}
const uploadFile = async (file: File) => {
  try {
    // 检测文件
    const checkResult = await checkFile(file, fileTypes)
    // 检测文件大小
    if (file.size > 100 * 1024 * 1024) {
      message.notifyWarning('The file size exceeds the limit')
      return false
    }
    // 检查通过，创建上传文件信息
    if (checkResult.can) {
      const fileInfo: FileInfo = {
        uid: nanoid(10),
        file: file,
        type: checkResult.type,
        folderId: 0, // 例如目录id，默认根目录
        relativePath: ''
      }
      // 添加到上传文件列表
      fileInfoList.value.push(fileInfo)
    }
  } catch {
  } finally {
    // uploading.value = false
  }
  return false
}

const deleteFile = (file: UploadResult, fileInfo: FileInfo) => {
  // 将fileInfo里的数据也清除掉
  fileInfoList.value = fileInfoList.value.filter((item) => item.uid !== fileInfo.uid)
  fileList.value = fileList.value.filter((item) => item.id !== file.id)
}
const downloadFile = (row: UploadResult) => {
  download.signature(row)
}
// 文件预览
const handlePreview = (file: UploadResult) => {
  const baseUrl = `${window.location.origin}/preview`
  // 构建查询字符串
  const queryParams = new URLSearchParams({
    url: file.url,
    title: file.fileName
  }).toString()
  const fullUrl = `${baseUrl}?${queryParams}`
  window.open(fullUrl, '_blank')
}

const handlePrepared = (fileItem) => {
  console.log(fileItem)
}

// 保存
const handleConfirm = async () => {
  // 组装最终传参数据
  if (formData.value.attachmentJson !== '') {
    formData.value.attachmentJson = formData.value.attachmentJson?.map((attachment, index) => {
      return {
        ...attachment,
        url: fileList.value[index]?.url
      }
    })
  }

  // 校验课程是否都已经学完
  if (formData.value.courseJson.some((course) => course.existStudy === false)) {
    message.info('Please finish the course first')
    return
  }

  // 校验文件是否都已经上传
  if (
    formData.value.attachmentJson &&
    formData.value.attachmentJson.some(
      (attachment) => !attachment?.url || attachment.url.trim() === ''
    )
  ) {
    message.info('Please upload the corresponding file')
    return
  }

  if (!bookCheck.value) {
    message.info('Please check the box to agree first')
    return
  }
  formData.value.userId = userStore.user.id
  const data = formData.value as ClassBookingSaveVO
  // 转化为json字符串格式
  data.courseJson = JSON.stringify(formData.value?.courseJson)
  data.attachmentJson = JSON.stringify(formData.value?.attachmentJson)
  try {
    formLoading.value = true
    await MlcTrainingApi.createClassBooking(data)
    // 发送操作成功的事件
    emit('success')
    message.success('Booking successful')
    isDialogOpen.value = false
  } finally {
    formLoading.value = false
  }
}

const handleComplete = (value: UploadResult) => {
  fileList.value.push(value)
}

defineExpose({
  openDialog
})
</script>

<template>
  <Dialog v-model:open="isDialogOpen">
    <DialogContent class="max-w-none w-[900px] max-h-[90vh] overflow-y-auto" disableOutsidePointerEvents>
      <DialogHeader class="pb-4 border-b">
        <DialogTitle class="text-xl font-semibold">Course Booking</DialogTitle>
        <p class="text-sm text-muted-foreground mt-1">Complete your course registration</p>
      </DialogHeader>

      <div v-loading="formLoading" class="space-y-6 py-4">
        <!-- Course Information Card -->
        <Card class="p-4">
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">
                {{ courseInfo?.title }}
              </h3>
              <div class="flex items-center gap-2">
                <Badge variant="secondary">
                  {{ classLanguageOptions.find(lang => lang.value === classInfo?.language)?.label }}
                </Badge>
                <Badge variant="outline">
                  {{ classTypes.find(type => type.value === classInfo?.type)?.label }}
                </Badge>
              </div>
            </div>
            <Badge
              :variant="courseInfo?.status === 1 ? 'default' : 'secondary'"
              class="ml-4"
            >
              {{ classBookingStatusOptions.find(status => status.value === courseInfo?.status)?.label }}
            </Badge>
          </div>

          <div class="space-y-3">
            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-1">Training Description</h4>
              <p class="text-sm text-muted-foreground">
                {{ courseInfo?.remarks || 'No description available' }}
              </p>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-1">Schedule</h4>
              <p class="text-sm text-muted-foreground">
                {{ classInfo?.startTime && classInfo?.endTime
                  ? `${classInfo?.startDate} ${classInfo?.startTime} ~ ${classInfo?.startDate} ${classInfo?.endTime}`
                  : 'Schedule not available'
                }}
              </p>
            </div>
          </div>
        </Card>
        <!-- Booking Information Card -->
        <Card class="p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Booking Information</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <h4 class="text-sm font-medium text-gray-700">Booking Period</h4>
              <div class="flex items-center gap-2">
                <Calendar class="w-4 h-4 text-muted-foreground" />
                <span class="text-sm text-amber-600 font-medium">
                  {{ formatDate(classInfo?.bookingStartTime) }} ~ {{ formatDate(classInfo?.bookingEndTime) }}
                </span>
              </div>
            </div>

            <div class="space-y-2">
              <h4 class="text-sm font-medium text-gray-700">Enrollment Status</h4>
              <div class="flex items-center gap-2">
                <Users class="w-4 h-4 text-muted-foreground" />
                <span class="text-sm">
                  <span class="text-amber-600 font-semibold">{{ classInfo?.assignNum || '0' }}</span>
                  <span class="text-muted-foreground"> / {{ classInfo?.maxNum }} enrolled</span>
                </span>
              </div>
            </div>
          </div>
        </Card>
        <!-- Prerequisites Card -->
        <Card class="p-4" v-if="courseList?.length > 0 || attachmentList?.length > 0">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Prerequisites</h3>

          <!-- Required Courses -->
          <div v-if="courseList?.length > 0" class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
              <BookOpen class="w-4 h-4" />
              Required Courses
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div
                v-for="course in courseList"
                :key="course.courseId"
                class="flex items-center justify-between p-3 border rounded-lg"
                :class="course.existStudy ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'"
              >
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 truncate">{{ course.courseName }}</p>
                </div>
                <div class="ml-2">
                  <CheckCircle v-if="course.existStudy" class="w-5 h-5 text-green-600" />
                  <XCircle v-else class="w-5 h-5 text-red-600" />
                </div>
              </div>
            </div>
          </div>

          <!-- Required Attachments -->
          <div v-if="attachmentList?.length > 0">
            <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
              <Paperclip class="w-4 h-4" />
              Required Documents
            </h4>
            <div class="flex flex-wrap gap-2 mb-4">
              <Badge v-for="attachment in attachmentList" :key="attachment" variant="outline" class="text-xs">
                {{ attachment.name }}
              </Badge>
            </div>
            <!-- File Upload Section -->
            <div v-show="attachmentList && fileList.length < attachmentList.length" class="mt-4 p-4 border-2 border-dashed border-gray-300 rounded-lg">
              <div class="text-center">
                <UploadIcon class="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p class="text-sm text-muted-foreground mb-3">Upload required documents in sequence</p>
                <!-- 隐藏的文件输入框 -->
                <input
                  ref="fileInputRef"
                  type="file"
                  class="hidden"
                  multiple
                  @change="handleFileChange"
                  :accept="'.pptx,.doc,.docx,.xls,.xlsx,.pdf'"
                />
                <!-- 触发文件选择的上传按钮 -->
                <Button variant="outline" @click="triggerFileSelect" class="w-full max-w-xs">
                  <UploadIcon class="w-4 h-4 mr-2" />
                  Choose Files
                </Button>
              </div>
            </div>
            <!-- 上传文件列表 -->
            <SuperUpload
              v-for="fileInfo of fileInfoList"
              :key="fileInfo"
              :file-info="fileInfo"
              @prepared="handlePrepared"
              @complete="handleComplete"
            >
              <template #default="{ progress, result }">
                <div class="flex items-center space-x-4">
                  <!-- 左侧文件信息 -->
                  <div class="flex-1 front-info">
                    <div class="flex items-center justify-between w-full cursor-pointer">
                      <div class="flex items-center w-full">
                        <!-- 文件名 -->
                        <!-- 文件名（固定宽度 + 截断省略号） -->
                        <div class="shrink-0 w-40 truncate">
                          <span>{{ fileInfo.file.name }}</span>
                        </div>
                        <!-- 进度条（使用自定义 LinearProgress 组件） -->
                        <div
                          class="flex items-center ms-4 w-60"
                          v-show="progress.percentage > 0 && progress.percentage < 100"
                        >
                          <Progress v-model="progress.percentage"></Progress>
                          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ progress.percentage }}%
                          </span>
                        </div>
                        <!-- 操作：完成 & 删除 -->
                        <div class="flex space-x-2 ms-auto" v-if="result.id">
                          <CheckCircleIcon class="w-3 h-3 text-green-500 check-icon" />
                          <TrashIcon
                            class="w-3 h-3 text-red-500 cursor-pointer close-icon"
                            @click="deleteFile(result, fileInfo)"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 右侧操作栏 -->
                  <div class="flex justify-end space-x-4" v-show="progress.percentage === 100">
                    <Button
                      class="h-6 text-primary"
                      @click="handlePreview(result)"
                      variant="outline"
                      ><EyeIcon />View</Button
                    >
                    <Button class="h-6 text-white" @click="downloadFile(result)"
                      ><DownloadIcon />Download</Button
                    >
                  </div>
                </div>
              </template>
            </SuperUpload>
          </div>
        </Card>

        <!-- Booking Type Selection -->
        <Card class="p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Booking Options</h3>
          <div class="flex mt-3 mb-3">
          <RadioGroup
            v-model="formData.type"
            @update:model-value="selectBookingType"
            class="flex gap-2"
          >
            <div
              v-for="bookingType in bookingTypeOptions"
              :key="bookingType.value"
              class="flex items-center gap-1"
            >
              <RadioGroupItem :value="bookingType.value" />
              <Label>{{ bookingType.label }}</Label>
            </div>
          </RadioGroup>
        </div>
          <div class="mb-3">
            <Button v-show="formData.type === 2" @click="addInvitee" variant="outline" size="sm">
              <Users class="w-4 h-4 mr-2" />
              Add Student
            </Button>
          </div>
        </Card>

        <!-- Student Information -->
        <Card class="p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Student Information</h3>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead class="w-[100px]"> Name </TableHead>
              <TableHead> Badge Number </TableHead>
              <TableHead> Company </TableHead>
              <TableHead> Position </TableHead>
              <TableHead class="text-right"> Full/Refresher </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="user in userList" :key="user.id">
              <TableCell class="font-medium">
                {{ user.name }}
              </TableCell>
              <TableCell>{{ user.badgeNumber }}</TableCell>
              <TableCell>{{ user.companyName }}</TableCell>
              <TableCell>{{ user.postName }}</TableCell>
              <TableCell>{{ user.isFirstStudy }}</TableCell>
            </TableRow>
          </TableBody>
        </Table>

        <!-- Terms and Confirmation -->
        <div class="mt-6 space-y-4">
          <div class="flex items-center p-3 bg-gray-50 rounded-lg">
            <Checkbox v-model="bookCheck" />
            <label for="terms" class="ml-2 text-sm text-gray-700">
              Please ensure that all submitted materials are true and valid.
            </label>
          </div>
          <div class="text-center">
            <Button @click="handleConfirm" :loading="true" class="w-full max-w-xs">
              <CheckCircle class="w-4 h-4 mr-2" />
              Book Now
            </Button>
          </div>
        </div>
        </Card>
      </div>
    </DialogContent>
  </Dialog>

  <UserPicker
    ref="UserPickerRef"
    :disabledUserList="disabledUserList"
    @confirm="handleUserConfirm"
  />
</template>

<style scoped lang="scss">
.acitve {
  @apply text-[#ffff]  rounded-3xl bg-linear-to-r from-[#0B61E9] to-[#8A71EC] pl-4 pr-4;
}
:deep .pagination-container {
  background: #edf6f2;
}
</style>
