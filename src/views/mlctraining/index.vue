<script setup lang="ts">
import { TrainingApi, TrainingRespVO } from '@/api/training/mlctraining'
import TrainingCard from './components/TrainingCard.vue'
import { cn } from '@/lib/utils'
import { CalendarIcon, Play, Trash } from 'lucide-vue-next'
import { SuperSearch } from '@/components/SuperSearch'
const router = useRouter()

const trainingList = ref<TrainingRespVO[]>([])
const queryParams = reactive({
  title: undefined,
  pageNo: 1,
  pageSize: 20
})
const loading = ref(false)
const total = ref(0)
const nodataShow = ref(false)
function handleSearch() {
  queryParams.pageNo = 1
  queryParams.pageSize = 20
  nodataShow.value = false
  getList()
}
// 获取课程信息
const getList = async () => {
  loading.value = true
  try {
    const res = await TrainingApi.getTrainingPage(queryParams)
    trainingList.value = res.list
    total.value = res.total
    if (res.list.length === 0) {
      nodataShow.value = true
    }
  } finally {
    loading.value = false
  }
}

const handleCurrentChange = (newPage: number) => {
  queryParams.pageNo = newPage
  getList()
}

const handleClick = (id: number) => {
  router.push({
    name: 'TrainingDetail',
    query: { id }
  })
}

onMounted(() => {
  getList()
})
</script>

<template>
  <ContainerWrapper>
    <template #content>
      <div class="h-full flex flex-col">
        <!-- Header Body -->
        <div class="flex items-center justify-between h-[52px] px-4">
          <!--Header-->
          <H2 class="text-xl font-semibold tracking-tight"> MLC Training </H2>

          <!--搜索框-->
          <SuperSearch
            v-model="queryParams.title"
            :loading="loading"
            @keyup="handleSearch"
            @search="handleSearch"
            placeholder="Search for content..."
          />
        </div>

        <!--分割线-->
        <Separator />

        <!--课程列表-->
        <ScrollArea class="flex-1">
          <div class="p-4">
            <!--空状态-->
            <EmptyPlaceholder v-if="total === 0" />

            <!--课程列表-->
            <div class="grid grid-cols-5 gap-4" v-if="trainingList.length > 0">
              <div v-for="(training, index) in trainingList" :key="index">
                <TrainingCard :item="training" @click="handleClick(training.id)" />
              </div>
            </div>
          </div>
        </ScrollArea>

        <!-- 固定在底部的分割线和分页器 -->
        <div class="mt-auto">
          <!--分割线-->
          <Separator />

          <SmartPagination
            v-if="total > 0"
            :total="total"
            :current-page="queryParams.pageNo"
            :page-size="queryParams.pageSize"
            @current-change="handleCurrentChange"
            class="px-5 py-3"
          />
        </div>
      </div>
    </template>
  </ContainerWrapper>
</template>
