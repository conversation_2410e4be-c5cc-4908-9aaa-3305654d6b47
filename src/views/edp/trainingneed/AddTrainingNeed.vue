<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArchiveX } from 'lucide-vue-next'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { TrainingNeedAPI, TrainingTypeEnum } from '@/api/edp/trainingneed'
import { AIChatApi } from '@/api/edp/chat'
import { getPositionListForCurrentUser } from '@/api/system/user'
import type { PostVO } from '@/api/system/post'
import {
  CirclePlus,
  Trash,
  Captions,
  Users,
  BicepsFlexed,
  Presentation,
  ChartColumnStacked,
  Check,
  X
} from 'lucide-vue-next'
import GenerateIcon from '@/assets/icons/generateIcon.svg'
import { ContainerWrapper } from '@/components/ContainerWrap'
import Return from '@/components/Return.vue'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { useMessage } from '@/hooks/web/useMessage'

const message = useMessage()

/** ----- INTERFACE ----- */
interface TrainingContent {
  id: number
  skillName: string
  trainingType: TrainingTypeEnum | undefined
  contentCatalog: string
  fullContentCatalog?: string // 用于存储完整内容
  lastGeneratedContent?: string // 保存上一次生成的内容
  checked?: boolean // 控制按钮显示状态
}

interface TrainingNeed {
  id: number
  title: string
  positionName: string
  content: TrainingContent[]
}

/** ----- SETUP ----- */
const props = defineProps<{
  id?: number // 培训申请ID
  type?: 'add' | 'edit' | 'view'
}>() // 定义父组件通信数据
const emit = defineEmits(['finish']) // 定义完成事件
const router = useRouter() // 路由实例
const route = useRoute() // 路由信息

// 根据路由自动判断模式和ID
const currentMode = computed(() => {
  console.log('计算 currentMode:')
  console.log('- props.type:', props.type)
  console.log('- route.query.type:', route.query.type)
  console.log('- route.name:', route.name)

  if (props.type) {
    console.log('- 使用 props.type:', props.type)
    return props.type
  }
  // 优先使用查询参数中的 type
  if (route.query.type) {
    console.log('- 使用 route.query.type:', route.query.type)
    return route.query.type as 'add' | 'edit' | 'view'
  }
  const defaultMode = route.name === 'EditTrainingNeed' ? 'edit' : 'add'
  console.log('- 使用默认模式:', defaultMode)
  return defaultMode
})

const currentId = computed(() => {
  if (props.id) return props.id
  return route.params.id ? Number(route.params.id) : undefined
})

const positionList = ref<PostVO[]>([]) // 岗位列表
const formErrors = ref({
  title: '',
  skills: [
    {
      skillName: '',
      trainingType: '',
      contentCatalog: ''
    }
  ] as Array<{
    skillName: string
    trainingType: string
    contentCatalog: string
  }>
}) // 表单验证错误信息
const trainingNeed = ref<TrainingNeed>({
  id: 0,
  title: '',
  positionName: '',
  content: [
    {
      id: 0,
      skillName: '',
      trainingType: undefined,
      contentCatalog: '',
      lastGeneratedContent: '',
      checked: false
    }
  ]
}) // 培训申请数据
const trainingTypeOptions = ref([
  { label: 'MLC Training', value: TrainingTypeEnum.MLC_TRAINING },
  { label: 'Online Training', value: TrainingTypeEnum.ONLINE_TRAINING },
  { label: 'On-Job Training', value: TrainingTypeEnum.ON_JOB_TRAINING }
]) // 培训类型选项

// 流式输出相关状态
const generatingIndex = ref<number | null>(null) // 当前正在生成的技能卡片索引
const abortController = ref<AbortController | null>(null) // 用于取消请求
const typingQueue = ref<string[]>([]) // 打字队列
const isTyping = ref(false) // 是否正在打字
// 移除了 appliedIndexes，现在使用 isUserGenerated 参数来控制按钮显示
const loading = ref(false) // 数据加载状态

// 计算属性：为每个技能解析表格数据
const parsedTableData = computed(() => {
  const result = trainingNeed.value.content.map((skill, index) => {
    const data = parseContentCatalog(skill.contentCatalog)
    console.log(`计算属性 - 技能 ${index} 解析数据:`, data)
    return data
  })
  console.log('计算属性 parsedTableData 完整结果:', result)
  return result
})

/** ----- FUNCTIONS ----- */
/** 解析内容目录JSON数据为表格数据 */
const parseContentCatalog = (contentCatalog: string) => {
  console.log('开始解析内容目录:', contentCatalog)

  if (!contentCatalog || contentCatalog.trim() === '') {
    console.log('内容为空，返回空数组')
    return []
  }

  const trimmedContent = contentCatalog.trim()
  console.log('处理后的内容:', trimmedContent)

  // 放宽检查条件，允许更多格式的内容
  if (trimmedContent.length < 5) {
    console.log('内容太短，返回空数组')
    return []
  }

  try {
    let parsedData: any

    // 尝试多种解析方式
    if (trimmedContent.startsWith('[') || trimmedContent.startsWith('{')) {
      // 直接解析JSON
      console.log('尝试直接解析JSON')
      parsedData = JSON.parse(trimmedContent)
    } else {
      // 尝试从文本中提取JSON数组
      console.log('尝试从文本中提取JSON数组')
      const arrayMatch = trimmedContent.match(/\[[\s\S]*\]/)
      if (arrayMatch) {
        console.log('找到数组匹配:', arrayMatch[0])
        parsedData = JSON.parse(arrayMatch[0])
      } else {
        // 尝试查找对象数组的模式
        const objectArrayMatch = trimmedContent.match(/\{[\s\S]*\}/)
        if (objectArrayMatch) {
          console.log('找到对象匹配，尝试包装为数组')
          // 如果找到对象，尝试将其包装为数组
          const objectStr = objectArrayMatch[0]
          // 检查是否是多个对象用逗号分隔
          if (objectStr.includes('},{')) {
            parsedData = JSON.parse(`[${objectStr}]`)
          } else {
            parsedData = [JSON.parse(objectStr)]
          }
        } else {
          console.log('未找到有效的JSON结构')
          return []
        }
      }
    }

    console.log('初步解析结果:', parsedData)

    // 如果解析结果是对象且包含 course_recommendations 字段
    if (parsedData && typeof parsedData === 'object' && parsedData.course_recommendations) {
      console.log('提取 course_recommendations 字段')
      parsedData = parsedData.course_recommendations
    }

    // 确保结果是数组
    if (!Array.isArray(parsedData)) {
      console.log('结果不是数组，尝试转换')
      if (typeof parsedData === 'object' && parsedData !== null) {
        // 如果是单个对象，包装为数组
        parsedData = [parsedData]
      } else {
        console.log('无法转换为数组，返回空数组')
        return []
      }
    }

    console.log('最终解析的数组:', parsedData)

    // 确保每个对象都有必要的字段，支持多种字段名称
    const result = parsedData.map((item: any, index: number) => {
      console.log(`处理数组项 ${index}:`, item)
      return {
        courseName: item['Course Name'] || item.courseName || item.name || item.title || 'N/A',
        duration: item['Duration'] || item.duration || item.time || 'N/A',
        level: item['Skill Level'] || item.level || item.difficulty || 'N/A',
        description: item['Detailed Content/Covered Topics'] || item['Key Skills Gained'] || item.description || item.desc || item.summary || 'N/A'
      }
    })

    console.log('最终表格数据:', result)
    return result
  } catch (error) {
    console.error('JSON解析失败:', error.message, '原始内容:', contentCatalog)
    return []
  }
}

/** 获取岗位列表 */
const getPositionList = async () => {
  try {
    const res = await getPositionListForCurrentUser()

    // 过滤相同name的项目，保留第一个出现的项目
    const uniquePositions = res.filter(
      (position: PostVO, index: number, array: PostVO[]) =>
        array.findIndex((item: PostVO) => item.name === position.name) === index
    )

    positionList.value = uniquePositions
    console.log('获取岗位列表成功:', positionList.value)
    console.log('去重前数量:', res.length, '去重后数量:', uniquePositions.length)
  } catch (error) {
    console.log('获取岗位列表失败:', error)
  }
}

/** 验证表单 */
const validateForm = () => {
  // 重置错误信息
  formErrors.value.title = ''
  formErrors.value.skills = trainingNeed.value.content.map(() => ({
    skillName: '',
    trainingType: '',
    contentCatalog: ''
  }))

  let isValid = true

  // 验证标题
  if (!trainingNeed.value.title.trim()) {
    formErrors.value.title = 'Title is required'
    isValid = false
  }

  // 验证技能（只验证前5个，排除第6个警告卡片）
  const validSkills = trainingNeed.value.content.slice(0, 5)
  validSkills.forEach((skill, index) => {
    if (!skill.skillName.trim()) {
      formErrors.value.skills[index].skillName = 'Skill name is required'
      isValid = false
    }

    if (!skill.trainingType) {
      formErrors.value.skills[index].trainingType = 'Training type is required'
      isValid = false
    }

    if (!skill.contentCatalog.trim()) {
      formErrors.value.skills[index].contentCatalog = 'Content catalog is required'
      isValid = false
    }
  })

  return isValid
}

/** 获得培训申请详情 */
const getTrainingNeed = async (id: number) => {
  try {
    loading.value = true // 开始加载
    // 调用API
    trainingNeed.value = await TrainingNeedAPI.getTrainingNeedDetail(id) // 保存数据到响应式变量
    console.log('获取培训申请成功😊:', trainingNeed.value)

    // 对于从编辑页面加载的已有数据，不需要显示 Apply/Reject 按钮
    // 因为这些数据不是用户刚刚生成的，所以不设置 checked 状态
    console.log('从编辑页面加载的数据，不设置checked状态')
  } catch (error) {
    console.log('获取培训申请失败😫:', error)
    message.error('Failed to load training need details')
  } finally {
    loading.value = false // 结束加载
  }
}

/** 添加技能 */
const addSkill = () => {
  // 最多允许添加6个技能卡片（第6个用于显示警告）
  if (trainingNeed.value.content.length >= 6) {
    return
  }

  trainingNeed.value.content.push({
    id: 0,
    skillName: '',
    trainingType: undefined,
    contentCatalog: '',
    lastGeneratedContent: '',
    checked: false
  })

  // 同步添加错误信息对象
  formErrors.value.skills.push({
    skillName: '',
    trainingType: '',
    contentCatalog: ''
  })
}

/** 打字效果处理队列 */
const processTypingQueue = async (skill: TrainingContent) => {
  if (typingQueue.value.length === 0) {
    // 确保显示完整内容
    if (skill.fullContentCatalog && skill.contentCatalog !== skill.fullContentCatalog) {
      skill.contentCatalog = skill.fullContentCatalog
    }
    isTyping.value = false
    return
  }

  isTyping.value = true
  const text = typingQueue.value.shift() || ''

  // 多重过滤确保没有 <think> 标签内容，并过滤空行
  let content = text
    .replace(/<think>[\s\S]*?<\/think>/gi, '') // 移除完整的think标签
    .replace(/<think>[\s\S]*$/gi, '') // 移除未闭合的think开始标签
    .replace(/^[\s\S]*?<\/think>/gi, '') // 移除未开始的think结束标签
    .split('\n') // 按行分割
    .filter((line: string) => line.trim() !== '') // 过滤空行
    .join('\n') // 重新组合
    .trim()

  console.log('打字机处理内容:', content)

  // 如果过滤后内容为空，继续处理下一个
  if (content === '') {
    await processTypingQueue(skill)
    return
  }

  // 逐字符打印文本
  for (let i = 0; i < content.length; i++) {
    // 再次检查当前字符是否是think标签的开始
    const remainingText = content.slice(i)
    if (remainingText.startsWith('<think>')) {
      // 跳过整个think标签
      const thinkEndIndex = remainingText.indexOf('</think>')
      if (thinkEndIndex !== -1) {
        i += thinkEndIndex + '</think>'.length - 1
        continue
      } else {
        // 如果没有结束标签，跳过剩余所有内容
        break
      }
    }

    skill.contentCatalog += content.charAt(i)
    await new Promise((resolve) => setTimeout(resolve, 20)) // 控制打字速度
  }

  // 处理队列中的下一条消息
  await processTypingQueue(skill)
}

/** 停止流式输出 */
const stopStream = () => {
  // 确保当前技能显示完整内容
  if (generatingIndex.value !== null) {
    const skill = trainingNeed.value.content[generatingIndex.value]
    if (skill && skill.fullContentCatalog && skill.contentCatalog !== skill.fullContentCatalog) {
      skill.contentCatalog = skill.fullContentCatalog
    }
  }

  if (abortController.value) {
    abortController.value.abort()
    abortController.value = null
  }
  generatingIndex.value = null
  isTyping.value = false
  typingQueue.value = []
}

/** 生成内容目录 */
const generateContentCatalog = async (index: number) => {
  if (generatingIndex.value !== null) {
    message.warning('正在生成中，请稍候...')
    return
  }

  const skill = trainingNeed.value.content[index]
  if (!skill.skillName) {
    message.warning('请先填写技能名称')
    return
  }

  generatingIndex.value = index
  // 保存当前内容到上一次生成的内容
  if (skill.contentCatalog && skill.contentCatalog.trim()) {
    skill.lastGeneratedContent = skill.contentCatalog
  }
  skill.contentCatalog = '' // 清空现有内容
  skill.fullContentCatalog = '' // 清空完整内容
  skill.checked = false // 重置checked状态
  typingQueue.value = [] // 清空打字队列
  isTyping.value = false // 重置打字状态
  // 重置状态，准备生成新内容

  // 创建新的AbortController
  abortController.value = new AbortController()

  let lastContent = '' // 上一次收到的完整内容

  const onMessage = (event: MessageEvent) => {
    try {
      const data = JSON.parse(event.data)
      let rawContent = data.data?.answer || ''

      console.log('原始内容:', rawContent)

      // 跳过系统日志信息和空白行
      if (rawContent.includes('is running...') || rawContent.trim() === '') {
        return
      }

      // 检查是否包含完整的JSON结构（判断流式输出是否完成）
      const hasCompleteJson =
        rawContent.includes('"courses"') &&
        rawContent.includes('[') &&
        rawContent.includes(']') &&
        !rawContent.includes('```json') // 确保不是刚开始的标记

      console.log('是否包含完整JSON:', hasCompleteJson)

      // 如果检测到完整内容，停止打字机并一次性显示最终内容
      if (hasCompleteJson) {
        console.log('检测到完整内容，停止打字机并一次性显示最终内容')

        // 停止打字机效果
        isTyping.value = false
        typingQueue.value = []

        // 提取并格式化最终内容
        const extractFinalContent = (content: string) => {
          try {
            let cleanContent = content
              .replace(/<think>[\s\S]*?<\/think>/gi, '') // 完整的think标签
              .replace(/<think>[\s\S]*$/gi, '') // 未闭合的think开始标签
              .replace(/^[\s\S]*?<\/think>/gi, '') // 未开始的think结束标签
              .replace(/```json/gi, '') // 移除 ```json 标记
              .replace(/```/gi, '') // 移除 ``` 标记
              .split('\n') // 按行分割
              .filter((line: string) => line.trim() !== '') // 过滤空行
              .join('\n') // 重新组合

            console.log('移除标签后:', cleanContent)

            // 查找 "courses": [ 开始的位置
            const coursesMatch = cleanContent.match(/"courses":\s*\[/)
            if (coursesMatch && coursesMatch.index !== undefined) {
              const startIndex = coursesMatch.index + coursesMatch[0].length - 1 // 包含 [

              // 从 [ 开始查找匹配的 ]
              let bracketCount = 0
              let endIndex = -1

              for (let i = startIndex; i < cleanContent.length; i++) {
                if (cleanContent[i] === '[') {
                  bracketCount++
                } else if (cleanContent[i] === ']') {
                  bracketCount--
                  if (bracketCount === 0) {
                    endIndex = i
                    break
                  }
                }
              }

              if (endIndex !== -1) {
                const jsonArrayStr = cleanContent.substring(startIndex, endIndex + 1)
                console.log('提取的courses数组:', jsonArrayStr)

                try {
                  const parsedArray = JSON.parse(jsonArrayStr)
                  if (Array.isArray(parsedArray) && parsedArray.length > 0) {
                    // 将数组中的每个对象格式化为多行，但不包含外层中括号
                    const formattedObjects = parsedArray.map((obj) => JSON.stringify(obj, null, 2))
                    return formattedObjects.join(',\n')
                  }
                } catch (parseError) {
                  console.log('JSON解析失败，手动处理:', parseError)
                  const trimmed = jsonArrayStr.trim()
                  if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
                    return trimmed.slice(1, -1).trim()
                  }
                  return trimmed
                }
              }
            }

            return cleanContent.trim()
          } catch (error) {
            console.error('提取最终内容失败:', error)
            return content
              .replace(/<think>[\s\S]*?<\/think>/gi, '')
              .replace(/```json/gi, '')
              .replace(/```/gi, '')
              .split('\n')
              .filter((line: string) => line.trim() !== '')
              .join('\n')
              .trim()
          }
        }

        const finalContent = extractFinalContent(rawContent)
        if (finalContent && finalContent.trim() !== '') {
          skill.contentCatalog = finalContent
          skill.fullContentCatalog = finalContent
          console.log('最终内容已一次性设置到textarea')

          // 检查表格是否渲染完成并有数据，设置checked状态（用户生成的内容）
          setTimeout(() => {
            checkTableRendered(index, true)
          }, 100) // 延迟一点时间确保DOM更新完成
        }

        lastContent = rawContent
        return
      }

      // 如果还没有完整内容，继续打字机效果
      // 先过滤掉不需要显示的内容和空行
      let filteredContent = rawContent
        .replace(/<think>[\s\S]*?<\/think>/gi, '') // 完整的think标签
        .replace(/<think>[\s\S]*$/gi, '') // 未闭合的think开始标签
        .replace(/^[\s\S]*?<\/think>/gi, '') // 未开始的think结束标签
        .replace(/```json/gi, '') // 移除 ```json 标记
        .replace(/```/gi, '') // 移除 ``` 标记
        .split('\n') // 按行分割
        .filter((line: string) => line.trim() !== '') // 过滤空行
        .join('\n') // 重新组合

      console.log('过滤后用于打字机的内容:', filteredContent)

      // 如果过滤后内容为空，直接返回
      if (!filteredContent || filteredContent.trim() === '') {
        return
      }

      // 如果是第一次接收内容
      if (lastContent === '') {
        const trimmedContent = filteredContent.trimStart()
        lastContent = rawContent // 存储原始内容用于完整性检测

        // 初始化 fullContentCatalog
        if (!skill.fullContentCatalog) {
          skill.fullContentCatalog = ''
        }
        skill.fullContentCatalog = trimmedContent

        if (trimmedContent.length > 0) {
          typingQueue.value.push(trimmedContent)
        }

        if (!isTyping.value) {
          processTypingQueue(skill)
        }
        return
      }

      // 获取新增部分
      if (rawContent.startsWith(lastContent)) {
        const rawNewPart = rawContent.slice(lastContent.length)
        // 对新增部分也进行过滤和空行过滤
        const filteredNewPart = rawNewPart
          .replace(/<think>[\s\S]*?<\/think>/gi, '')
          .replace(/<think>[\s\S]*$/gi, '')
          .replace(/^[\s\S]*?<\/think>/gi, '')
          .replace(/```json/gi, '')
          .replace(/```/gi, '')
          .split('\n') // 按行分割
          .filter((line: string) => line.trim() !== '') // 过滤空行
          .join('\n') // 重新组合

        if (filteredNewPart.length === 0) {
          lastContent = rawContent // 更新原始内容记录
          return
        }

        lastContent = rawContent
        skill.fullContentCatalog = filteredContent // 更新完整内容

        if (filteredNewPart.length > 0) {
          typingQueue.value.push(filteredNewPart)
        }

        if (!isTyping.value) {
          processTypingQueue(skill)
        }
      } else {
        // 内容不连续，重置
        lastContent = rawContent
        skill.contentCatalog = ''
        skill.fullContentCatalog = filteredContent
        typingQueue.value = []

        if (filteredContent.length > 0) {
          typingQueue.value.push(filteredContent)
        }

        if (!isTyping.value) {
          processTypingQueue(skill)
        }
      }
    } catch (error) {
      console.error('解析消息失败:', error)
    }
  }

  const onError = (error: Event) => {
    console.error('Stream error:', error)
    message.error('生成失败，请重试')
    stopStream()
  }

  const onClose = () => {
    console.log('Stream closed')

    // 流式输出结束时，如果还没有处理完整内容，进行最后一次处理
    if (lastContent && (!skill.contentCatalog || skill.contentCatalog.includes('```'))) {
      console.log('流式结束，进行最后处理:', lastContent)

      // 提取最终内容
      const extractFinalContent = (content: string) => {
        try {
          let cleanContent = content
            .replace(/<think>[\s\S]*?<\/think>/gi, '')
            .replace(/<think>[\s\S]*$/gi, '')
            .replace(/^[\s\S]*?<\/think>/gi, '')
            .replace(/```json/gi, '')
            .replace(/```/gi, '')
            .split('\n') // 按行分割
            .filter((line: string) => line.trim() !== '') // 过滤空行
            .join('\n') // 重新组合

          // 查找并格式化数组内容
          const coursesMatch = cleanContent.match(/"courses":\s*\[/)
          if (coursesMatch && coursesMatch.index !== undefined) {
            const startIndex = coursesMatch.index + coursesMatch[0].length - 1
            let bracketCount = 0
            let endIndex = -1

            for (let i = startIndex; i < cleanContent.length; i++) {
              if (cleanContent[i] === '[') bracketCount++
              else if (cleanContent[i] === ']') {
                bracketCount--
                if (bracketCount === 0) {
                  endIndex = i
                  break
                }
              }
            }

            if (endIndex !== -1) {
              const jsonArrayStr = cleanContent.substring(startIndex, endIndex + 1)
              try {
                const parsedArray = JSON.parse(jsonArrayStr)
                if (Array.isArray(parsedArray) && parsedArray.length > 0) {
                  const formattedObjects = parsedArray.map((obj) => JSON.stringify(obj, null, 2))
                  return formattedObjects.join(',\n')
                }
              } catch (parseError) {
                const trimmed = jsonArrayStr.trim()
                if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
                  return trimmed.slice(1, -1).trim()
                }
                return trimmed
              }
            }
          }

          return cleanContent.trim()
        } catch (error) {
          return content
            .replace(/<think>[\s\S]*?<\/think>/gi, '')
            .replace(/```json/gi, '')
            .replace(/```/gi, '')
            .split('\n')
            .filter((line: string) => line.trim() !== '')
            .join('\n')
            .trim()
        }
      }

      const finalContent = extractFinalContent(lastContent)
      if (finalContent && finalContent.trim() !== '') {
        skill.contentCatalog = finalContent
        skill.fullContentCatalog = finalContent
        console.log('流式结束时设置最终内容')

        // 检查表格是否渲染完成并有数据，设置checked状态（用户生成的内容）
        setTimeout(() => {
          checkTableRendered(index, true)
        }, 100) // 延迟一点时间确保DOM更新完成
      }
    }

    stopStream()

    // 在停止流式输出后检查表格渲染状态，确保按钮能正确显示
    setTimeout(() => {
      checkTableRendered(index, true)
    }, 200) // 稍微延长延迟时间确保所有处理完成
  }

  // 构建请求数据
  const requestData = {
    positionName: trainingNeed.value.positionName || 'IT',
    skillName: skill.skillName
  }

  try {
    await AIChatApi.getContentCatalog(
      requestData,
      abortController.value,
      onMessage,
      onError,
      onClose
    )
  } catch (error) {
    console.error('生成内容目录失败:', error)
    message.error('生成失败，请重试')
    stopStream()
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  stopStream()
})

/** 拒绝当前生成的内容，恢复到上一次的内容或清空 */
const rejectContent = (index: number) => {
  const skill = trainingNeed.value.content[index]
  if (skill.lastGeneratedContent) {
    // 如果有上一次的内容，恢复到上一次
    skill.contentCatalog = skill.lastGeneratedContent
    skill.lastGeneratedContent = ''
  } else {
    // 如果没有上一次的内容（第一次生成），清空内容
    skill.contentCatalog = ''
  }
  // 设置checked为false，隐藏按钮
  skill.checked = false
}

/** 检查表格是否渲染完成并有数据，设置checked状态 */
const checkTableRendered = (index: number, isUserGenerated: boolean = false) => {
  const skill = trainingNeed.value.content[index]
  const parsedData = parsedTableData.value[index] || []
  const hasData = parsedData.length > 0
  const notGenerating = generatingIndex.value !== index

  console.log(`检查技能 ${index} 表格渲染状态:`)
  console.log('- 原始内容:', skill.contentCatalog)
  console.log('- 解析数据:', parsedData)
  console.log('- 有数据:', hasData)
  console.log('- 未在生成:', notGenerating)
  console.log('- 是否用户生成:', isUserGenerated)
  console.log('- 当前checked状态:', skill.checked)

  // 只有在表格渲染完成、有数据且是用户刚刚生成的内容时才设置为true
  const shouldCheck = hasData && notGenerating && isUserGenerated
  skill.checked = shouldCheck

  console.log('- 设置checked为:', shouldCheck)
}

/** 应用当前生成的内容，保存到 contentCatalog 并隐藏按钮 */
const applyContent = (index: number) => {
  const skill = trainingNeed.value.content[index]
  // 当前内容已经在 contentCatalog 中，只需要清空历史记录并设置checked为false
  skill.lastGeneratedContent = ''
  skill.checked = false
}

/** 保存培训需求到接口 */
const saveTrainingNeed = async () => {
  // 验证表单
  if (!validateForm()) {
    message.error('请完善表单信息')
    return
  }

  try {
    // 过滤掉无效的技能内容
    const validContent = trainingNeed.value.content.filter(
      (skill) => skill.skillName.trim() && skill.trainingType && skill.contentCatalog.trim()
    )

    if (validContent.length === 0) {
      message.error('请至少添加一个完整的技能内容')
      return
    }

    // 构建请求数据
    const requestData = {
      title: trainingNeed.value.title,
      content: validContent.map((skill) => ({
        id: skill.id || undefined,
        skillName: skill.skillName,
        contentType: Number(skill.trainingType),
        contentCatalog: skill.contentCatalog,
        status: 1
      })),
      status: 1 // 草稿状态
    }

    // 根据不同的状态来判断是更新还是创建
    if (currentMode.value === 'edit' || trainingNeed.value.id) {
      // 更新培训申请
      const updateData = {
        ...requestData,
        id: trainingNeed.value.id
      }
      await TrainingNeedAPI.updateTrainingNeed(updateData)
      message.success('保存成功')
    } else {
      // 创建培训申请
      const res = await TrainingNeedAPI.createTrainingNeed(requestData)
      // 更新ID以便后续操作
      if (res && res.id) {
        trainingNeed.value.id = res.id
      }
      message.success('保存成功')
    }
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败，请重试')
  }
}

/** 删除技能 */
const removeSkill = (index: number) => {
  if (trainingNeed.value.content.length > 1) {
    trainingNeed.value.content.splice(index, 1)
    // 同步删除错误信息
    formErrors.value.skills.splice(index, 1)
  }
}

/** 保存&添加技能表单 */
const handleSubmit = async (contentStatus: number) => {
  // 验证表单
  if (!validateForm()) {
    message.warning('Please fill in all required fields')
    return
  }

  // 如果有第6个技能卡片，只提交前5个
  const validContent = trainingNeed.value.content.slice(0, 5)

  try {
    // 创建请求体
    const requestData = {
      title: trainingNeed.value.title,

      content: validContent.map((skill) => ({
        skillName: skill.skillName,
        trainingType: Number(skill.trainingType),
        contentCatalog: skill.contentCatalog,
        status: 1
      })),
      status: contentStatus // 根据不同按钮传递的contentStatus来区分保存和提交
    }

    // 根据不同的状态来判断是更新还是创建
    if (currentMode.value === 'edit' || trainingNeed.value.id) {
      // 更新培训申请
      const updateData = {
        ...requestData,
        id: trainingNeed.value.id,
        content: validContent.map((skill) => ({
          id: skill.id,
          skillName: skill.skillName,
          trainingType: Number(skill.trainingType),
          contentCatalog: skill.contentCatalog,
          status: 1
        }))
      }
      const res = await TrainingNeedAPI.updateTrainingNeed(updateData)
      console.log('更新成功😊:', res)
    } else {
      // 添加培训申请
      const res = await TrainingNeedAPI.createTrainingNeed(requestData)
      console.log('提交成功😊:', res)
    }
    emit('finish')

    // 显示成功消息
    message.success(contentStatus === 1 ? 'Saved successfully' : 'Submitted successfully')

    // 重置表单，以便下次打开
    resetForm()

    // 返回上一级
    router.back()
  } catch (error) {
    console.log('提交失败😫:', error)
    message.error('Operation failed, please try again')
  }
}

/** 重置表单 */
const resetForm = () => {
  trainingNeed.value = {
    id: 0,
    title: '',
    positionName: '',
    content: [
      {
        id: 0,
        skillName: '',
        trainingType: undefined,
        contentCatalog: '',
        lastGeneratedContent: '',
        checked: false
      }
    ]
  }

  // 重置错误信息
  formErrors.value.title = ''
  formErrors.value.skills = [
    {
      skillName: '',
      trainingType: '',
      contentCatalog: ''
    }
  ]
}

/** ----- LIFECYCLE HOOK ----- */
onMounted(async () => {
  // 组件挂载时，先获取岗位列表
  await getPositionList()

  if (currentId.value) {
    // 如果有ID，获取培训申请详情
    await getTrainingNeed(currentId.value)
  } else {
    // 如果没有ID，重置表单
    resetForm()
  }
})

// 监听 title 变化，清除错误提示
watch(
  () => trainingNeed.value.title,
  (newVal) => {
    if (newVal && newVal.trim()) {
      formErrors.value.title = ''
    }
  }
)

// 监听技能字段变化，清除相应的错误提示
watch(
  () => trainingNeed.value.content,
  (newContent) => {
    newContent.forEach((skill, index) => {
      if (formErrors.value.skills[index]) {
        // 监听技能名称
        if (skill.skillName && skill.skillName.trim()) {
          formErrors.value.skills[index].skillName = ''
        }

        // 监听培训类型
        if (skill.trainingType) {
          formErrors.value.skills[index].trainingType = ''
        }

        // 监听内容目录
        if (skill.contentCatalog && skill.contentCatalog.trim()) {
          formErrors.value.skills[index].contentCatalog = ''
        }
      }
    })
  },
  { deep: true }
)
</script>

<template>
  <ContainerWrapper v-if="$route.path === '/edp/training-need/add' || $route.name === 'EditTrainingNeed'">
    <template #content>
      <div class="h-full flex flex-col">
        <!--Header-->
        <div class="flex items-center justify-between">
          <Return :title="currentMode === 'edit' ? 'Edit Training Need' : 'Add Training Need'" />

          <!--保存&提交-->
          <div class="flex gap-2 px-4">
            <!--提交-->
            <Button v-if="props.type !== 'view'" variant="outline" @click="handleSubmit(1)">
              Save
            </Button>

            <!--提交-->
            <Button v-if="props.type !== 'view'" type="submit" @click="handleSubmit(2)" :disabled="trainingNeed.content.length >= 6">
              Submit
            </Button>
          </div>
        </div>

        <!--分割线-->
        <Separator />

        <!-- 内容 -->
        <div v-loading="loading" class="flex flex-col gap-5 p-4 flex-1 min-h-0">
          <!-- 标题和岗位信息的响应式栅格布局 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!--标题-->
            <div class="flex items-center justify-start w-full gap-2">
              <Captions />
              <div class="w-full">
                <Input
                  v-model="trainingNeed.title"
                  placeholder="Title"
                  :class="['h-10', formErrors.title ? 'border-red-500' : '']"
                />
                <p v-if="formErrors.title" class="text-sm text-red-500 mt-1">
                  {{ formErrors.title }}
                </p>
              </div>
            </div>

            <!--岗位信息-->
            <div class="flex items-center justify-start w-full gap-2">
              <Users />
              <div class="w-full">
                <Select v-model="trainingNeed.positionName">
                  <SelectTrigger :class="['h-10', formErrors.title ? 'border-red-500' : '']">
                    <SelectValue placeholder="Please select a position" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="position in positionList"
                      :key="position.id"
                      :value="position.name || '-- No Data --'"
                    >
                      {{ position.name }}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="formErrors.title" class="text-sm text-red-500 mt-1">
                  {{ formErrors.title }}
                </p>
              </div>
            </div>
          </div>

          <!--技能表单-->
          <div class="flex flex-col items-center justify-start w-full gap-1 flex-1 min-h-0">
            <Alert v-show="trainingNeed.content.length >= 6" variant="destructive" class="mb-2">
              <ArchiveX class="h-4 w-4" />
              <AlertTitle class="">Oops!</AlertTitle>
              <AlertDescription class="">
                You can only add up to 5 skill cards per submission.
              </AlertDescription>
            </Alert>

            <!--技能列表标题-->
            <div class="flex w-full items-center justify-between">
              <Label for="name" class="w-full text-left"> Skills </Label>
              <Button
                variant="ghost"
                size="icon"
                :disabled="trainingNeed.content.length >= 6"
                @click="addSkill"
              >
                <CirclePlus />
              </Button>
            </div>

            <!-- 技能卡片列表 -->
            <ScrollArea class="w-full flex-1">
              <div
                v-for="(skill, index) in trainingNeed.content"
                :key="index"
                :class="[
                  'border rounded-lg w-full h-full p-3 bg-sidebar space-y-3',
                  index !== trainingNeed.content.length - 1 ? 'mb-4' : '',
                  index === 5 ? 'border-red-500' : ''
                ]"
              >
                <!-- 卡片内部栅格布局 -->
                <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 p-3 bg-white rounded-lg">
                  <!--技能名称&培训类型 - 4个栅格-->
                  <div class="lg:col-span-4 flex flex-col gap-3">
                    <!--技能名称-->
                    <div class="flex items-center gap-2">
                      <BicepsFlexed class="w-5 h-5" />
                      <div class="flex-1">
                        <Input
                          v-model="skill.skillName"
                          placeholder="Please input the skill you need"
                          :class="[
                            'h-10',
                            formErrors.skills[index]?.skillName ? 'border-red-500' : ''
                          ]"
                        />
                        <p
                          v-if="formErrors.skills[index]?.skillName"
                          class="text-sm text-red-500 mt-1"
                        >
                          {{ formErrors.skills[index].skillName }}
                        </p>
                      </div>
                    </div>

                    <!--培训类型-->
                    <div class="flex items-center gap-2">
                      <Presentation class="w-5 h-5" />
                      <div class="flex-1">
                        <Select v-model="skill.trainingType">
                          <SelectTrigger
                            :class="[
                              'pr-2',
                              formErrors.skills[index]?.trainingType ? 'border-red-500' : ''
                            ]"
                          >
                            <SelectValue placeholder="Select Training Type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectLabel> Training Type </SelectLabel>
                              <SelectItem
                                v-for="trainingTypeOption in trainingTypeOptions"
                                :key="trainingTypeOption.value"
                                :value="trainingTypeOption.value"
                              >
                                {{ trainingTypeOption.label }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <p
                          v-if="formErrors.skills[index]?.trainingType"
                          class="text-sm text-red-500 mt-1"
                        >
                          {{ formErrors.skills[index].trainingType }}
                        </p>
                      </div>
                    </div>
                  </div>

                  <!--课程目录 - 8个栅格-->
                  <div class="lg:col-span-8 flex flex-col gap-2">
                    <div
                      v-if="!skill.contentCatalog || skill.contentCatalog.trim() === ''"
                      class="flex flex-col items-center justify-center gap-2 p-4 text-muted-foreground"
                    >
                      <ChartColumnStacked />
                      <p class="text-center"> You can fill in the information to generate the course category. </p>
                    </div>

                    <div
                      v-else
                      v-loading="generatingIndex === index"
                      element-loading-text="Generating Content Catalog..."
                      :class="[
                        'border rounded-md p-4 min-h-[120px]',
                        formErrors.skills[index]?.contentCatalog ? 'border-red-500' : ''
                      ]"
                    >
                      <!-- 有数据时显示表格 -->
                      <div v-if="generatingIndex !== index && parsedTableData[index] && parsedTableData[index].length > 0" class="overflow-x-auto">
                        <table class="w-full border-collapse">
                          <thead>
                            <tr class="border-b">
                              <th class="text-left p-2 font-medium">Course Name</th>
                              <th class="text-left p-2 font-medium">Duration</th>
                              <th class="text-left p-2 font-medium">Level</th>
                              <th class="text-left p-2 font-medium">Description</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              v-for="(course, courseIndex) in parsedTableData[index]"
                              :key="courseIndex"
                              class="border-b hover:bg-gray-50"
                            >
                              <td class="p-2">{{ course.courseName }}</td>
                              <td class="p-2">{{ course.duration }}</td>
                              <td class="p-2">{{ course.level }}</td>
                              <td class="p-2">{{ course.description }}</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <!-- 生成中状态或空状态 -->
                      <div v-else class="flex flex-col items-center justify-center h-full gap-2">
                        <p v-if="generatingIndex === index" class="text-muted-foreground text-center">
                          Generating content...
                        </p>
                        <p v-else class="text-muted-foreground text-center">
                          No course data available
                        </p>
                      </div>
                    </div>
                    <p
                      v-if="formErrors.skills[index]?.contentCatalog"
                      class="text-sm text-red-500 mt-1"
                    >
                      {{ formErrors.skills[index].contentCatalog }}
                    </p>
                  </div>
                </div>

                <!--卡片底部文字描述&操作按钮组-->
                <div class="flex items-center justify-between gap-2">
                  <p class="text-sm text-muted-foreground w-full truncate">
                    TIN's Learning Suggestions by Self-Input or AI Input.
                  </p>

                  <!--按钮组-->
                  <div class="flex items-center justify-end">
                    <!-- Reject 和 Apply 按钮 (只有在用户生成内容后才显示) -->
                    <div v-if="skill.checked" class="flex items-center gap-1 mr-2">
                      <!-- Reject 按钮 -->
                      <Button
                        variant="ghost"
                        size="icon"
                        class="hover:text-red-500 transition-colors"
                        title="Reject current content"
                        @click="rejectContent(index)"
                      >
                        <X class="w-4 h-4" />
                      </Button>

                      <!-- Apply 按钮 -->
                      <Button
                        variant="ghost"
                        size="icon"
                        class="hover:text-green-500 transition-colors"
                        title="Apply current content"
                        @click="applyContent(index)"
                      >
                        <Check class="w-4 h-4" />
                      </Button>
                    </div>

                    <!-- 流式输出生成按钮 -->
                    <Button
                      variant="ghost"
                      size="icon"
                      :disabled="
                        generatingIndex === index ||
                        !trainingNeed.positionName ||
                        !trainingNeed.content[index]?.skillName
                      "
                      @click="generateContentCatalog(index)"
                    >
                      <img :src="GenerateIcon" alt="Generate Icon" />
                    </Button>

                    <!--删除按钮-->
                    <Button
                      v-if="trainingNeed.content.length > 1"
                      variant="ghost"
                      size="icon"
                      class="hover:text-red-500 transition-colors"
                      @click="removeSkill(index)"
                    >
                      <Trash />
                    </Button>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </div>
        </div>
      </div>
    </template>
  </ContainerWrapper>
</template>