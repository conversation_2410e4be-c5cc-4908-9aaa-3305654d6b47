<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  TrainingNeedAPI,
  TrainingNeedRespVO,
  TrainingNeedStatusEnum,
  TrainingTypeEnum
} from '@/api/edp/trainingneed'
import { Trash, Eye, Plus, SquarePen } from 'lucide-vue-next'
import { SuperSearch } from '@/components/SuperSearch'
import TrainingNeedDialog from '@/views/edp/trainingneed/TrainingNeedDialog.vue'
import { Separator } from '@/components/ui/separator'

/** ----- INTERFACE ----- */
interface PageParams extends PageParam {
  title: string
  statusList: number | number[] | undefined
  trainingType: TrainingTypeEnum | undefined
} // 分页请求参数

interface Option {
  label: string
  value: number
} // 下拉选项类型

/** ----- SETUP ----- */
const router = useRouter() // 路由实例
const TrainingNeedList = ref<TrainingNeedRespVO[]>([]) // 培训申请列表
const total = ref(0) // 总页数
const loading = ref(false) // 加载状态
const queryParams = reactive<PageParams>({
  pageNo: 1,
  pageSize: 10,
  title: '',
  statusList: undefined,
  trainingType: undefined
}) // 查询参数
const trainingTypes = ref<Option[]>([
  { label: 'MLC Training', value: TrainingTypeEnum.MLC_TRAINING },
  { label: 'Online Training', value: TrainingTypeEnum.ONLINE_TRAINING },
  { label: 'On-Job Training', value: TrainingTypeEnum.ON_JOB_TRAINING }
]) // 培训类型选项
const statusOptions = ref<Option[]>([
  { label: 'Draft', value: TrainingNeedStatusEnum.DRAFT },
  { label: 'Submitted', value: TrainingNeedStatusEnum.SUBMITTED },
  { label: 'Approved', value: TrainingNeedStatusEnum.APPROVED },
  { label: 'Rejected', value: TrainingNeedStatusEnum.REJECTED }
]) // 状态选项

/** ----- FUNCTIONS ----- */
/** 获得培训申请分页 */
const getList = async () => {
  loading.value = true
  try {
    const res = await TrainingNeedAPI.getTrainingNeedPage(queryParams)
    TrainingNeedList.value = res.list
    total.value = res.total
    console.log('获得培训申请分页成功😊:', TrainingNeedList.value)
  } catch (error) {
    console.error('获得培训申请分页失败☹️:', error)
  } finally {
    loading.value = false
  }
}

/** 根据trainingType获取对应的label */
const getTrainingTypeLabel = (type: number) => {
  const found = trainingTypes.value.find((item) => item.value === type)
  return found ? found.label : 'Unknown'
}

/** 根据status获取对应的label */
const getStatusLabel = (status: number) => {
  const found = statusOptions.value.find((item) => item.value === status)
  return found ? found.label : 'Unknown'
}

/** 删除培训申请 */
const handleDelete = async (id: number) => {
  try {
    // 调用API
    const res = await TrainingNeedAPI.deleteTrainingNeed(id)
    console.log('删除培训申请成功😊:', res)
    await getList()
  } catch (error) {
    console.log('删除培训申请失败😫:', error)
  }
}

// 分页器获取页面变更
const handleCurrentChange = async (newPage: number) => {
  queryParams.pageNo = newPage
  await getList()
}

/** 跳转至添加培训需求页面 */
const toAdd = () => {
  router.push('/edp/training-need/add')
}

/** 跳转至编辑需求页面 */
const toEdit = (id: number) => {
  router.push({
    path: `/edp/training-need/edit/${id}`
  })
}

/** ----- LIFECYCLE HOOK   */
onMounted(() => {
  getList()
})
</script>

<template>
  <ContainerWrapper v-if="$route.path === '/edp/training-need'">
    <template #content>
      <div class="h-full flex flex-col">
        <div class="flex items-center justify-between px-5 py-3 gap-2">
          <!--Header-->
          <H2 class="text-xl font-semibold tracking-tight"> Training Need </H2>

          <!--添加操作-->
          <Button @click="toAdd">
            <Plus class="size-4" />
            Add New TNI
          </Button>
        </div>

        <!--分割线-->
        <Separator />

        <!--条件-->
        <div class="flex items-center gap-2 px-5 py-3">
          <!--搜索框-->
          <SuperSearch
            v-model="queryParams.title"
            @keyup="getList"
            @search="getList"
            placeholder="Search for Title..."
          />

          <!--培训类型筛选-->
          <Select v-model="queryParams.trainingType" @update:model-value="getList">
            <SelectTrigger class="w-auto">
              <SelectValue placeholder="Training Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel> Training Type </SelectLabel>
                <SelectItem v-for="type in trainingTypes" :key="type.value" :value="type.value">
                  {{ type.label }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>

          <!--状态筛选-->
          <Select v-model="queryParams.statusList" @update:model-value="getList">
            <SelectTrigger class="w-auto">
              <SelectValue placeholder="Status" class="mr-3" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel> Status </SelectLabel>
                <SelectItem
                  v-for="status in statusOptions"
                  :key="status.value"
                  :value="status.value"
                >
                  {{ status.label }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <!--列表容器 - 使用flex-1占据剩余空间-->
        <div class="flex-1 flex flex-col mx-5 min-h-0">
          <div v-loading="loading" class="rounded-md border flex flex-col">
            <Table class="rounded-md">
              <TableHeader>
                <TableRow>
                  <TableHead> Title </TableHead>
                  <TableHead> Skill </TableHead>
                  <TableHead> Training Type </TableHead>
                  <TableHead> Status </TableHead>
                  <TableHead> Actions </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <template v-for="trainingNeed in TrainingNeedList" :key="trainingNeed.id">
                  <template v-for="(content, index) in trainingNeed.content" :key="content.id">
                    <TableRow>
                      <!--标题-->
                      <TableCell
                        v-if="index === 0"
                        :rowspan="trainingNeed.content.length"
                        class="hover:bg-transparent"
                      >
                        {{ trainingNeed.title }}
                      </TableCell>

                      <!--技能名称-->
                      <TableCell>{{ content.skillName }}</TableCell>

                      <!--培训类型-->
                      <TableCell>{{ getTrainingTypeLabel(content.trainingType) }}</TableCell>

                      <!--状态-->
                      <TableCell v-if="index === 0" :rowspan="trainingNeed.content.length">
                        <Badge variant="secondary">
                          {{ getStatusLabel(trainingNeed.status) }}
                        </Badge>
                      </TableCell>

                      <!--操作按钮-->
                      <TableCell
                        v-if="index === 0"
                        :rowspan="trainingNeed.content.length"
                        class="space-x-2"
                      >
                        <!--编辑-->
                        <Button
                          v-if="trainingNeed.status === TrainingNeedStatusEnum.DRAFT"
                          variant="outline"
                          :size="'icon'"
                          class="hover:text-destructive hover:border-destructive cursor-pointer"
                          @click="toEdit(trainingNeed.id)"
                        >
                          <SquarePen class="size-4" />
                        </Button>

                        <!--预览-->
                        <TrainingNeedDialog
                          v-if="trainingNeed.status !== TrainingNeedStatusEnum.DRAFT"
                          :id="trainingNeed.id"
                          type="view"
                          dialog-title="Training Need"
                          dialog-description="View the request for the resources you need."
                        >
                          <Button variant="outline" :size="'icon'" class="cursor-pointer">
                            <Eye class="size-4" />
                          </Button>
                        </TrainingNeedDialog>

                        <!--删除-->
                        <Button
                          variant="outline"
                          :size="'icon'"
                          class="hover:text-destructive hover:border-destructive cursor-pointer"
                          @click="handleDelete(trainingNeed.id)"
                        >
                          <Trash class="size-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  </template>
                </template>
              </TableBody>
            </Table>
            <EmptyPlaceholder v-if="TrainingNeedList.length === 0" />
          </div>
        </div>

        <!--固定在底部的分页组件-->
        <div v-if="TrainingNeedList.length > 0" class="mt-auto">
          <!--分割线-->
          <Separator />
          <!--分页组件-->
          <div class="px-5 py-4">
            <SmartPagination
              :total="total"
              :current-page="queryParams.pageNo!"
              :page-size="queryParams.pageSize!"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </template>
  </ContainerWrapper>

  <!-- 子路由渲染区域 -->
  <router-view />
</template>