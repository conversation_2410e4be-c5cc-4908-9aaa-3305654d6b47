<script setup lang="ts" name="Journey">
import JourneyList from '@/views/journey/components/journeyList/index.vue'
import { listJourney, getJourneyCategoryAll } from '@/api/journey'
import { Button } from '@/components/ui/button'
import { RefreshCcw, X } from 'lucide-vue-next'
import { SuperSearch } from '@/components/SuperSearch'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import EmptyPlaceholder from '@/components/EmptyPlaceholder.vue'
import { SmartPagination } from '@/components/SmartPagination'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

interface JourneyItem {
  completed: boolean
  courseCount: number
  courseDuration: string
  courseIds: Array<string>
  cover: string
  id: number
  introduction: string
  keywords: string
  status: number
  studentCount: number
  title: string
}

const journeyList = ref<Array<JourneyItem>>([])
const queryParams = ref({
  title: '',
  categoryId: 'all', // 默认为"全部分类"
  pageNum: 1,
  pageSize: 20
})
const total = ref(0)
const loading = ref(false)
const nodataShow = ref(false)
// Learning journey category list
const journeyCategoryList = ref([])
const getDataList = async () => {
  const params = {
    ...queryParams.value
  }
  // Clean up undefined values and handle "all" category
  if (!params.categoryId || params.categoryId === 'all') {
    delete params.categoryId
  }
  if (!params.title) {
    delete params.title
  }

  try {
    loading.value = true
    nodataShow.value = false
    const data = await listJourney(params)
    console.log('Journey API response:', data)
    if (data && data.list) {
      journeyList.value = data.list
      total.value = data.total || data.list.length
      if (data.list.length === 0) {
        nodataShow.value = true
      }
    } else {
      journeyList.value = []
      total.value = 0
      nodataShow.value = true
    }
  } catch (error) {
    console.error('Failed to load journey list:', error)
    journeyList.value = []
    total.value = 0
    nodataShow.value = true
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  queryParams.value.pageNum = 1
  queryParams.value.pageSize = 20
  nodataShow.value = false
  getDataList()
}

const handleReset = () => {
  queryParams.value.pageSize = 20
  queryParams.value.pageNum = 1
  queryParams.value.title = ''
  queryParams.value.categoryId = 'all' // 重置为"全部分类"
  handleSearch()
}

const handleCurrentChange = (newPage: number) => {
  queryParams.value.pageNum = newPage
  getDataList()
}

const clearCategory = () => {
  queryParams.value.categoryId = 'all'
  handleSearch()
}

// Get learning journey categories
const getListCategory = async () => {
  try {
    const res = await getJourneyCategoryAll()
    journeyCategoryList.value = res
  } catch (error) {
    console.error('Failed to load categories:', error)
  }
}
onMounted(() => {
  getDataList()
  getListCategory()
})
</script>

<template>
  <!-- Learning Journey 查询表单 -->
  <ContainerWrapper class="flex space-x-5">
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="h-full flex items-center justify-between px-6 py-4 bg-white border-b">
            <!-- Filter Section -->
            <div class="flex items-center gap-4">
              <!-- Search Input -->
              <div class="flex items-center gap-2">
                <SuperSearch
                  v-model="queryParams.title"
                  placeholder="Search learning journeys..."
                  @search="handleSearch"
                  clearable
                  @clear="queryParams.title = ''"
                  @keyup="handleSearch"
                  class="w-full max-w-md"
                />
              </div>

              <!-- Category Filter -->
              <div class="flex items-center gap-2">
                <Select v-model="queryParams.categoryId" @update:model-value="handleSearch">
                  <SelectTrigger class="w-[200px]">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <!-- All Categories Option -->
                    <SelectItem value="all"> All Categories </SelectItem>
                    <!-- Category Options -->
                    <SelectItem
                      v-for="category in journeyCategoryList"
                      :key="category.id"
                      :value="category.id"
                    >
                      {{ category.title }}
                    </SelectItem>
                  </SelectContent>
                </Select>

                <!-- Clear Category Button -->
                <!--                <Button-->
                <!--                  v-if="queryParams.categoryId && queryParams.categoryId !== 'all'"-->
                <!--                  variant="ghost"-->
                <!--                  size="icon"-->
                <!--                  class="h-8 w-8"-->
                <!--                  @click="clearCategory"-->
                <!--                  title="Clear category filter"-->
                <!--                >-->
                <!--                  <X class="w-4 h-4" />-->
                <!--                </Button>-->
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center gap-2">
              <Button variant="outline" size="sm" @click="handleReset" class="text-xs">
                <RefreshCcw class="w-4 h-4 mr-1" />
                Reset
              </Button>
            </div>
          </div>
        </template>

        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 p-6"
        >
          <div v-for="(item, index) in journeyList" :key="`${item.id}_${index}`" class="h-full">
            <JourneyList :data="item" />
          </div>
        </div>

        <EmptyPlaceholder v-if="nodataShow" />

        <template #footer v-if="total > 0">
          <SmartPagination
            :current-page="queryParams.pageNum"
            :page-size="queryParams.pageSize"
            :total="total"
            @current-change="handleCurrentChange"
          />
        </template>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss">
// Remove old styles as they're now handled by modern components
</style>
