<script setup lang="ts">
import {
  AudioWaveform,
  Blocks,
  Calendar,
  Command,
  Home,
  Inbox,
  MessageCircleQuestion,
  Search,
  Settings2,
  Sparkles,
  Trash2,
  SquareUserRound,
  ShieldUser
} from 'lucide-vue-next'

import NavFavorites from '@/components/NavFavorites.vue'
import NavMain from '@/components/NavMain.vue'
import NavSecondary from '@/components/NavSecondary.vue'
import NavWorkspaces from '@/components/NavWorkspaces.vue'
import TeamSwitcher from '@/components/TeamSwitcher.vue'
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  type SidebarProps,
  SidebarRail
} from '@/components/ui/sidebar'

import { navs, type INav, NavTypeEnum } from '@/layout/config'
import { useAppStore } from '@/store/modules/app'
import { useSidebar } from '@/components/ui/sidebar/utils'
const props = defineProps<SidebarProps>()
const { state, setOpen } = useSidebar()

// 监听state状态变化
watch(
  () => state.value,
  () => {
    console.log('Sidebar 当前状态:', state.value)
    useAppStore().setSidebarCollapsed(state.value === 'collapsed')
  }
)

// watchEffect(() => {
//   console.log('Sidebar 当前状态:', state.value)
//   useAppStore().setSidebarCollapsed(state.value === 'collapsed')
// })

// This is sample data.
const data = {
  teams: [
    {
      name: 'Online Learning Platform',
      logo: SquareUserRound,
      plan: 'User-End'
    },
    {
      name: 'Admin-End',
      logo: ShieldUser,
      plan: 'Admin-End'
    }
  ],
  navMain: [
    // {
    //   title: 'Search',
    //   url: '#',
    //   icon: Search
    // },
    {
      title: 'Ask AI',
      url: '#',
      icon: Sparkles
    },
    {
      title: 'Home',
      url: '#',
      icon: Home,
      isActive: true
    },
    {
      title: 'Inbox',
      url: '#',
      icon: Inbox,
      badge: '10'
    }
  ],
  navSecondary: [
    {
      title: 'Calendar',
      url: '#',
      icon: Calendar
    },
    // {
    //   title: 'Settings',
    //   url: '#',
    //   icon: Settings2
    // },
    // {
    //   title: 'Templates',
    //   url: '#',
    //   icon: Blocks
    // },
    // {
    //   title: 'Trash',
    //   url: '#',
    //   icon: Trash2
    // },
    // {
    //   title: 'Help',
    //   url: '#',
    //   icon: MessageCircleQuestion
    // }
  ],
  favorites: [
    {
      name: 'Project Management & Task Tracking',
      url: '#',
      emoji: '📊'
    },
    {
      name: 'Family Recipe Collection & Meal Planning',
      url: '#',
      emoji: '🍳'
    },
    {
      name: 'Fitness Tracker & Workout Routines',
      url: '#',
      emoji: '💪'
    },
    {
      name: 'Book Notes & Reading List',
      url: '#',
      emoji: '📚'
    },
    {
      name: 'Sustainable Gardening Tips & Plant Care',
      url: '#',
      emoji: '🌱'
    },
    {
      name: 'Language Learning Progress & Resources',
      url: '#',
      emoji: '🗣️'
    },
    {
      name: 'Home Renovation Ideas & Budget Tracker',
      url: '#',
      emoji: '🏠'
    },
    {
      name: 'Personal Finance & Investment Portfolio',
      url: '#',
      emoji: '💰'
    },
    {
      name: 'Movie & TV Show Watchlist with Reviews',
      url: '#',
      emoji: '🎬'
    },
    {
      name: 'Daily Habit Tracker & Goal Setting',
      url: '#',
      emoji: '✅'
    }
  ],
  notes: [
    {
      name: 'Personal Life Management',
      emoji: '🏠',
      pages: [
        {
          name: 'Daily Journal & Reflection',
          url: '#',
          emoji: '📔'
        },
        {
          name: 'Health & Wellness Tracker',
          url: '#',
          emoji: '🍏'
        },
        {
          name: 'Personal Growth & Learning Goals',
          url: '#',
          emoji: '🌟'
        }
      ]
    },
    {
      name: 'Professional Development',
      emoji: '💼',
      pages: [
        {
          name: 'Career Objectives & Milestones',
          url: '#',
          emoji: '🎯'
        },
        {
          name: 'Skill Acquisition & Training Log',
          url: '#',
          emoji: '🧠'
        },
        {
          name: 'Networking Contacts & Events',
          url: '#',
          emoji: '🤝'
        }
      ]
    },
    {
      name: 'Creative Projects',
      emoji: '🎨',
      pages: [
        {
          name: 'Writing Ideas & Story Outlines',
          url: '#',
          emoji: '✍️'
        },
        {
          name: 'Art & Design Portfolio',
          url: '#',
          emoji: '🖼️'
        },
        {
          name: 'Music Composition & Practice Log',
          url: '#',
          emoji: '🎵'
        }
      ]
    },
    {
      name: 'Home Management',
      emoji: '🏡',
      pages: [
        {
          name: 'Household Budget & Expense Tracking',
          url: '#',
          emoji: '💰'
        },
        {
          name: 'Home Maintenance Schedule & Tasks',
          url: '#',
          emoji: '🔧'
        },
        {
          name: 'Family Calendar & Event Planning',
          url: '#',
          emoji: '📅'
        }
      ]
    },
    {
      name: 'Travel & Adventure',
      emoji: '🧳',
      pages: [
        {
          name: 'Trip Planning & Itineraries',
          url: '#',
          emoji: '🗺️'
        },
        {
          name: 'Travel Bucket List & Inspiration',
          url: '#',
          emoji: '🌎'
        },
        {
          name: 'Travel Journal & Photo Gallery',
          url: '#',
          emoji: '📸'
        }
      ]
    }
  ]
}

const mainNav = ref<INav[]>(navs.filter((_nav) => _nav.type === NavTypeEnum.MAIN))
const secondaryNav = ref<INav[]>(navs.filter((_nav) => _nav.type === NavTypeEnum.SECONDARY))
const toolNav = ref<INav[]>(navs.filter((_nav) => _nav.type === NavTypeEnum.TOOL))

onMounted(() => {
  // setOpen(!useAppStore().getSidebarCollapsed)
})
</script>

<template>
  <Sidebar class="border-r-0" v-bind="props">
    <SidebarHeader>
      <TeamSwitcher :teams="data.teams" />
      <!--      <Logo />-->
      <NavMain :items="mainNav" class="lg:hidden" />
      <NavMain :items="toolNav" />
    </SidebarHeader>
    <SidebarContent>
<!--      <NavFavorites :favorites="data.favorites" />-->
<!--      <NavWorkspaces :notes="data.notes" />-->
      <NavSecondary :items="secondaryNav" class="mt-auto" />
    </SidebarContent>
  </Sidebar>
</template>
