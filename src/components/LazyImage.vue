<template>
  <div
    ref="imageContainer"
    :class="['lazy-image-container', cn('h-auto w-auto transition-all hover:scale-105', classes)]"
    class="relative overflow-hidden"
  >
    <!-- 占位图 -->
    <div
      v-if="!state.isLoaded && !state.isError"
      class="placeholder absolute inset-0 flex items-center justify-center bg-muted"
    >
      <slot v-if="slots.placeholder" name="placeholder" />
      <!-- 自定义占位图标 -->
      <div v-else class="loading-spinner">
        <Icon name="LoaderIcon" class="animate-spin w-6 h-6 text-foreground/50" />
      </div>
    </div>

    <!-- 成功加载的图片 -->
    <img
      v-if="state.isLoaded"
      :src="src"
      :alt="alt"
      class="absolute inset-0 w-full h-full object-cover"
    />

    <!-- 错误提示 -->
    <div
      v-if="state.isError"
      class="error-message absolute inset-0 flex items-center justify-center text-sm bg-muted"
    >
      <img :src="loadError" alt="load error" class="w-20"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { cn } from '@/lib/utils'
import { useLazyLoad } from '@/hooks/web/useLazyLoad'
import loadError from '@/assets/imgs/load-error.png'

const slots = useSlots()

interface Props {
  src: string
  alt?: string
  aspectRatio?: 'portrait' | 'square'
  delay?: number // 延迟时间（可选）
}

const props = withDefaults(defineProps<Props>(), {
  aspectRatio: 'portrait',
  delay: 500
})

// 动态生成 aspectRatio 类名
const classes = computed(() => {
  return props.aspectRatio === 'portrait' ? 'aspect-3/4' : 'aspect-square'
})

const imageContainer = ref<HTMLElement | null>(null)
const [state, setElement] = useLazyLoad(props.src, props.delay)

onMounted(() => {
  setElement(imageContainer.value)
})
</script>
