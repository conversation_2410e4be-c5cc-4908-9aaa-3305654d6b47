<script setup lang="ts">
import { INav } from '@/layout/config'
const router = useRouter()
defineProps<{
  items: INav[]
}>()
</script>

<template>
  <SidebarGroup>
    <SidebarGroupContent>
      <SidebarMenu>
        <SidebarMenuItem v-for="item in items" :key="item.title">
          <SidebarMenuButton as-child :is-active="item.isActive" :tooltip="item.title">
            <div @click="router.push(item.url)">
              <component :is="item.icon" />
              <span>{{ item.title }}</span>
            </div>
          </SidebarMenuButton>
          <SidebarMenuBadge v-if="item.badge && item.badge > 0">
            {{ item.badge }}
          </SidebarMenuBadge>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroupContent>
  </SidebarGroup>
</template>
