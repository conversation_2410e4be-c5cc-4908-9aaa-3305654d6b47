<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Calendars from '@/components/Calendars.vue'
import DatePicker from '@/components/DatePicker.vue'
import {
  Sidebar,
  SidebarContent,
  type SidebarProps
} from '@/components/ui/sidebar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Card, CardContent } from '@/components/ui/card'
import { ArrowUpRight } from 'lucide-vue-next'
import { useRightCalendarSync } from '@/views/calendar/hooks/useCalendarSync'
import dayjs, { type Dayjs } from 'dayjs'
import {
  CalendarApi,
  type CalendarParams,
  getActivityTypeName,
  type CalendarActivityBO
} from '@/api/learning/calendar' // 导入日历 API 和类型

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: 'none'
})

// 任务类型配置
const taskTypes = [
  { key: 'MLC Training', label: 'MLC Training', color: '#0079b4' },
  { key: 'Live', label: 'Live', color: '#017B3D' },
]

// 根据任务类型名称获取颜色
const getTaskTypeColor = (typeName: string) => {
  const taskType = taskTypes.find(type => type.key === typeName)
  return taskType ? taskType.color : '#6B7280' // 默认灰色
}

// 状态管理
const selectedDate = ref<Dayjs>(dayjs())
const selectedTaskTypes = ref<string[]>(['MLC Training', 'Live'])
const tasks = ref<CalendarActivityBO[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// 同步机制
const { syncWithMainCalendar, onMainCalendarChange } = useRightCalendarSync()

// 路由
const router = useRouter()

// 筛选后的任务列表
const filteredTasks = computed(() => {
  if (selectedTaskTypes.value.length === 0) {
    return tasks.value
  }

  return tasks.value.filter(task => {
    const taskTypeName = getActivityTypeName(task.activityType)
    return selectedTaskTypes.value.includes(taskTypeName)
  })
})

// Calendar data with task types
const data = computed(() => ({
  user: {
    name: 'shadcn',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg'
  },
  calendars: [
    {
      name: 'My Calendar',
      items: taskTypes.map((type) => ({
        key: type.key,
        label: type.label,
        color: type.color,
        checked: selectedTaskTypes.value.includes(type.key)
      }))
    }
  ]
}))

// API 数据获取任务列表
const getTasks = async () => {
  if (loading.value) return

  loading.value = true
  error.value = null

  try {
    const startOfDay = selectedDate.value.startOf('day')
    const endOfDay = selectedDate.value.endOf('day')

    const params: CalendarParams = {
      startTime: startOfDay.format('YYYY-MM-DDTHH:mm:ss'),
      endTime: endOfDay.format('YYYY-MM-DDTHH:mm:ss')
    }

    const response = await CalendarApi.getCalendarInfo(params)
    console.log('API响应:', response)

    if (response) {
      // 扁平化处理activities数据
      const flattenedTasks: CalendarActivityBO[] = []

      // 遍历每个日期的数据
      response.forEach((dateItem) => {
        const activities = dateItem.activities || {}
        console.log('当前日期的activities:', activities)

        // 遍历activities对象，将所有活动扁平化到一个数组中
        Object.entries(activities).forEach(([activityType, activityList]) => {
          console.log(`活动类型 ${activityType}:`, activityList)
          activityList.forEach((activity) => {
            flattenedTasks.push(activity)
          })
        })
      })

      tasks.value = flattenedTasks
      console.log('扁平化后的 tasks:', tasks.value)
    } else {
      tasks.value = []
      console.log('没有数据，清空 tasks')
    }
  } catch (err) {
    console.error('Failed to load calendar data:', err)
    error.value = '加载日历数据失败'
    tasks.value = []
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleDateSelect = async (date: Dayjs) => {
  console.log('SidebarRight: DatePicker选择日期', date.format('YYYY-MM-DD'))
  selectedDate.value = date
  await getTasks()
  syncWithMainCalendar('date-change', { date })
}

const handleTaskTypeToggle = (taskType: string) => {
  const index = selectedTaskTypes.value.indexOf(taskType)
  if (index > -1) {
    selectedTaskTypes.value.splice(index, 1)
  } else {
    selectedTaskTypes.value.push(taskType)
  }

  // 同步筛选状态到主日历
  syncWithMainCalendar('filter-change', {
    selectedTypes: [...selectedTaskTypes.value]
  })

  console.log('已同步筛选状态到主日历:', selectedTaskTypes.value)
}

// 处理卡片点击跳转
const handleTaskClick = (task: CalendarActivityBO) => {
  const typeName = getActivityTypeName(task.activityType)

  if (typeName === 'MLC Training') {
    // 跳转到MLC Training详情页面，传递roomId作为id
    router.push({
      path: '/training/detail',
      query: { id: task.classId }
    })
    console.log('跳转到MLC Training详情页面，传递roomId作为id', task.roomId);
    
  } else if (typeName === 'Live') {
    // 跳转到Live详情页面，传递roomId作为id
    router.push({
      path: '/live/detail',
      query: { id: task.roomId }
    })
  }
}

// 监听主日历变化
onMainCalendarChange(async (event: any, data: any) => {
  if (event === 'date-change' && data.date && !data.date.isSame(selectedDate.value, 'day')) {
    console.log('SidebarRight: 接收到主日历日期变化', data.date.format('YYYY-MM-DD'))
    selectedDate.value = data.date
    await getTasks()
  }
})

// 组件挂载时加载数据
onMounted(async () => {
  await getTasks()

  // 初始同步筛选状态到主日历
  syncWithMainCalendar('filter-change', {
    selectedTypes: [...selectedTaskTypes.value]
  })
  console.log('SidebarRight初始筛选状态已同步:', selectedTaskTypes.value)
})

// 辅助方法
const formatTaskTime = (activity: CalendarActivityBO) => {
  if (!activity.startTime) return '时间待定'

  const start = dayjs(activity.startTime)
  if (!activity.endTime) {
    return start.format('HH:mm')
  }

  const end = dayjs(activity.endTime)
  return `${start.format('HH:mm')} - ${end.format('HH:mm')}`
}

const getStatusText = (status: number) => {
  switch (status) {
    case 10:
      return 'Available'
    case 20:
      return 'Pending Approval'
    case 30:
      return 'Booking Rejected'
    case 40:
      return 'In Progress'
    case 50:
      return 'Passed'
    case 60:
      return 'Failed'
    case 70:
      return 'Absent'
    case 90:
      return 'Booked'
    default:
      return ''
  }
}
</script>

<template>
  <Sidebar class="h-full" v-bind="props">
    <SidebarContent class="overflow-visible">
      <DatePicker
        :selected-date="selectedDate"
        @date-select="handleDateSelect"
      />
      <SidebarSeparator class="mx-0" />
      <Calendars
        :calendars="data.calendars"
        :selected-types="selectedTaskTypes"
        @task-type-toggle="handleTaskTypeToggle"
      />

      <!-- 任务列表 -->
      <div class="flex flex-col h-full overflow-hidden">
        <div class="px-4 py-2 flex-shrink-0">
          <h3 class="text-sm font-medium">Tasks for {{ selectedDate.format('MMM D') }}</h3>
        </div>

        <!-- 滚动内容区域 -->
        <div class="flex-1 pb-1 overflow-hidden" v-loading="loading">
          <ScrollArea class="h-full w-full px-4 pb-4 mt-1">
            <!-- 无任务状态 -->
            <div v-if="!loading && filteredTasks.length === 0" class="text-center text-gray-500 text-sm py-4">
              No tasks
            </div>

            <!-- 任务列表 - 遍历筛选后的任务数组 -->
            <div class="space-y-2">
              <Card
                v-for="(task, index) in filteredTasks"
                :key="`task-${index}`"
                class="cursor-pointer hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-200 relative group"
                @click="handleTaskClick(task)"
              >
                <CardContent class="p-3 pt-3">
                  <div class="flex items-start gap-2">
                    <div
                      class="w-3 h-3 rounded-full mt-1 flex-shrink-0"
                      :style="{ backgroundColor: getTaskTypeColor(getActivityTypeName(task.activityType)) }"
                    ></div>
                    <div class="flex-1 min-w-0">
                      <div class="font-medium text-sm truncate">{{ task.activityName }}</div>
                      <div class="text-xs text-gray-500 mt-1">
                        {{ formatTaskTime(task) }}
                      </div>
                      <div class="text-xs text-blue-600 mt-1">
                        {{ getActivityTypeName(task.activityType) }}
                      </div>
                      <!-- 任务状态 -->
                      <div class="text-xs text-gray-400 mt-1">
                        Status: {{ getStatusText(task.status || 10) }}
                      </div>
                      <!-- 房间ID -->
                      <div v-if="task.roomId" class="text-xs text-gray-400 mt-1">
                        Room: {{ task.roomId }}
                      </div>
                    </div>
                  </div>
                  
                  <!-- Hover时显示的箭头图标 -->
                  <ArrowUpRight
                    class="absolute top-2 right-2 w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 transform -translate-x-1 translate-y-1 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-200"
                  />
                </CardContent>
              </Card>
            </div>
          </ScrollArea>
        </div>
      </div>
    </SidebarContent>
  </Sidebar>
</template>
