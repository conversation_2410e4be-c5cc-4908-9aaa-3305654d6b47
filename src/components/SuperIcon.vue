<template>
  <div 
    class="rounded-lg flex items-center justify-center"
    :class="[
      sizeClasses,
      backgroundClasses,
      paddingClasses
    ]"
  >
    <component 
      :is="icon" 
      :class="[iconSizeClasses, textColorClasses]"
    />
  </div>
</template>

<script setup lang="ts">
import type { LucideIcon } from 'lucide-vue-next'

interface Props {
  icon: LucideIcon
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'blue' | 'red' | 'green' | 'purple' | 'yellow' | 'indigo' | 'orange' | 'amber' | 'gray' | 'emerald' | 'violet'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  color: 'gray'
})

// 容器尺寸类
const sizeClasses = computed(() => {
  const sizeMap = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10', 
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }
  return sizeMap[props.size]
})

// 内边距类
const paddingClasses = computed(() => {
  const paddingMap = {
    sm: 'p-1',
    md: 'p-2',
    lg: 'p-2.5',
    xl: 'p-3'
  }
  return paddingMap[props.size]
})

// 图标尺寸类
const iconSizeClasses = computed(() => {
  const iconSizeMap = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6', 
    xl: 'w-8 h-8'
  }
  return iconSizeMap[props.size]
})

// 背景颜色类
const backgroundClasses = computed(() => {
  const bgMap = {
    blue: 'bg-blue-100',
    red: 'bg-red-100',
    green: 'bg-green-100',
    emerald: 'bg-emerald-100',
    purple: 'bg-purple-100',
    violet: 'bg-violet-100',
    yellow: 'bg-yellow-100',
    indigo: 'bg-indigo-100',
    orange: 'bg-orange-100',
    amber: 'bg-amber-100',
    gray: 'bg-gray-100'
  }
  return bgMap[props.color]
})

// 文字颜色类
const textColorClasses = computed(() => {
  const textMap = {
    blue: 'text-blue-600',
    red: 'text-red-600',
    green: 'text-green-600',
    emerald: 'text-emerald-600',
    purple: 'text-purple-600',
    violet: 'text-violet-600',
    yellow: 'text-yellow-600',
    indigo: 'text-indigo-600',
    orange: 'text-orange-600',
    amber: 'text-amber-600',
    gray: 'text-gray-600'
  }
  return textMap[props.color]
})
</script>
