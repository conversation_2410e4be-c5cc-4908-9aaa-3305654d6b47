<template>
  <div
    class="fixed right-0 top-1/2 transform -translate-y-1/2 bg-white shadow-lg rounded-l-xl p-1 space-y-2 border" style="z-index: 30"
  >
    <div
      v-for="action of actions"
      :key="action.name"
      class="w-12 h-12 rounded cursor-pointer hover:bg-muted flex flex-col items-center justify-center text-center"
      @click="action.handler"
    >
      <Icon :name="action.icon" />
      <span class="text-[10px]">{{ action.name }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface Action {
  name: string
  icon: string
  handler: () => void
}

interface Props {
  actions: Action[]
}

const props = defineProps<Props>()
</script>

<style scoped></style>
