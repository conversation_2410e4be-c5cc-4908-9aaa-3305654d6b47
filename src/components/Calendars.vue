<script setup lang="ts">
import { toRefs } from 'vue'
import { Check, ChevronRight } from 'lucide-vue-next'

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from '@/components/ui/sidebar'

const props = defineProps<{
  calendars: {
    name: string
    items: {
      key: string
      label: string
      color: string
      checked: boolean
    }[]
  }[]
  selectedTypes?: string[]
}>()

const { selectedTypes } = toRefs(props)

const emit = defineEmits<{
  'task-type-toggle': [taskType: string]
}>()
</script>

<template>
  <template v-for="(calendar, index) in calendars" :key="calendar.name">
    <SidebarGroup class="py-0">
      <Collapsible
        :default-open="index === 0"
        class="group/collapsible"
      >
        <SidebarGroupLabel
          as-child
          class="group/label w-full text-sm text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
        >
          <CollapsibleTrigger>
            {{ calendar.name }}
            <ChevronRight class="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
          </CollapsibleTrigger>
        </SidebarGroupLabel>
        <CollapsibleContent>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem v-for="item in calendar.items" :key="item.key">
                <SidebarMenuButton @click="emit('task-type-toggle', item.key)">
                  <div
                    :data-active="selectedTypes?.includes(item.key) ?? item.checked"
                    class="group/calendar-item flex aspect-square size-4 shrink-0 items-center justify-center rounded-sm border border-sidebar-border text-sidebar-primary-foreground data-[active=true]:border-sidebar-primary data-[active=true]:bg-sidebar-primary"
                    :style="{
                      backgroundColor: (selectedTypes?.includes(item.key) ?? item.checked) ? item.color : 'transparent',
                      borderColor: item.color
                    }"
                  >
                    <Check class="hidden size-3 group-data-[active=true]/calendar-item:block text-white" />
                  </div>
                  {{ item.label }}
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </CollapsibleContent>
      </Collapsible>
    </SidebarGroup>
    <SidebarSeparator class="mx-0" />
  </template>
</template>
