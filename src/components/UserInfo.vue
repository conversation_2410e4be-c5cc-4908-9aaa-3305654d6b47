<script lang="ts" setup>
import avatarImg from '@/assets/imgs/avatar.gif'
import { useDesign } from '@/hooks/web/useDesign'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useUserStore } from '@/store/modules/user'
import { useLockStore } from '@/store/modules/lock'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'

import {
  Cloud,
  CreditCard,
  Github,
  Keyboard,
  LifeBuoy,
  LogOut,
  Mail,
  MessageSquare,
  Plus,
  PlusCircle,
  Settings,
  User,
  UserPlus,
  Users
} from 'lucide-vue-next'
import { DropdownMenuPortal } from 'reka-ui'

const { wsCache } = useCache()
defineOptions({ name: 'UserInfo' })

const { t } = useI18n()

const { push, replace } = useRouter()

const userStore = useUserStore()

const tagsViewStore = useTagsViewStore()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('user-info')

const avatar = computed(() => userStore.user.avatar || avatarImg)
const nickname = computed(() => userStore.user.nickname ?? 'Admin')
const email = computed(() => userStore.user.email ?? '<EMAIL>')
// 登录类型
const loginType = ref(0)
// 锁定屏幕
const lockStore = useLockStore()
const getIsLock = computed(() => lockStore.getLockInfo?.isLock ?? false)
const dialogVisible = ref<boolean>(false)
const lockScreen = () => {
  dialogVisible.value = true
}

const handleLogout = async () => {
  try {
    loginType.value = wsCache.get('loginType')
    if (loginType.value == 1 || loginType.value == 2) {
      await userStore.loginOut()
    } else if (loginType.value == 3) {
      await userStore.mfaLoginOut()
    } else {
    }
    tagsViewStore.delAllViews()
    replace('/login?redirect=/home')
  } catch {}
}
const toProfile = async () => {
  push('/user/profile')
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <div
        class="flex items-center cursor-pointer hover:bg-muted/50 rounded-md px-2 py-1 transition-colors"
      >
        <Avatar class="h-6 w-6 2xl:h-8 2xl:w-8 me-1 2xl:me-2">
          <AvatarImage :src="avatar" :alt="avatar" />
        </Avatar>
        <span class="hidden 2xl:inline-block text-sm truncate max-w-[120px]">{{ nickname }}</span>
      </div>
    </DropdownMenuTrigger>
    <DropdownMenuContent class="w-56">
      <DropdownMenuLabel>[角色]</DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem @click="toProfile">
          <User class="mr-2 h-4 w-4" />
          <span>Profile</span>
          <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
        </DropdownMenuItem>
        <!--        <DropdownMenuItem>-->
        <!--          <CreditCard class="mr-2 h-4 w-4" />-->
        <!--          <span>Billing</span>-->
        <!--          <DropdownMenuShortcut>⌘B</DropdownMenuShortcut>-->
        <!--        </DropdownMenuItem>-->
        <!--        <DropdownMenuItem>-->
        <!--          <Settings class="mr-2 h-4 w-4" />-->
        <!--          <span>Settings</span>-->
        <!--          <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>-->
        <!--        </DropdownMenuItem>-->
        <!--        <DropdownMenuItem>-->
        <!--          <Keyboard class="mr-2 h-4 w-4" />-->
        <!--          <span>Keyboard shortcuts</span>-->
        <!--          <DropdownMenuShortcut>⌘K</DropdownMenuShortcut>-->
        <!--        </DropdownMenuItem>-->
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <!--      <DropdownMenuGroup>-->
      <!--        <DropdownMenuItem>-->
      <!--          <Users class="mr-2 h-4 w-4" />-->
      <!--          <span>Team</span>-->
      <!--        </DropdownMenuItem>-->
      <!--        <DropdownMenuSub>-->
      <!--          <DropdownMenuSubTrigger>-->
      <!--            <UserPlus class="mr-2 h-4 w-4" />-->
      <!--            <span>Invite users</span>-->
      <!--          </DropdownMenuSubTrigger>-->
      <!--          <DropdownMenuPortal>-->
      <!--            <DropdownMenuSubContent>-->
      <!--              <DropdownMenuItem>-->
      <!--                <Mail class="mr-2 h-4 w-4" />-->
      <!--                <span>Email</span>-->
      <!--              </DropdownMenuItem>-->
      <!--              <DropdownMenuItem>-->
      <!--                <MessageSquare class="mr-2 h-4 w-4" />-->
      <!--                <span>Message</span>-->
      <!--              </DropdownMenuItem>-->
      <!--              <DropdownMenuSeparator />-->
      <!--              <DropdownMenuItem>-->
      <!--                <PlusCircle class="mr-2 h-4 w-4" />-->
      <!--                <span>More...</span>-->
      <!--              </DropdownMenuItem>-->
      <!--            </DropdownMenuSubContent>-->
      <!--          </DropdownMenuPortal>-->
      <!--        </DropdownMenuSub>-->
      <!--        <DropdownMenuItem>-->
      <!--          <Plus class="mr-2 h-4 w-4" />-->
      <!--          <span>New Team</span>-->
      <!--          <DropdownMenuShortcut>⌘+T</DropdownMenuShortcut>-->
      <!--        </DropdownMenuItem>-->
      <!--      </DropdownMenuGroup>-->
      <!--      <DropdownMenuSeparator />-->
      <!--      <DropdownMenuItem>-->
      <!--        <Github class="mr-2 h-4 w-4" />-->
      <!--        <span>GitHub</span>-->
      <!--      </DropdownMenuItem>-->
      <!--      <DropdownMenuItem>-->
      <!--        <LifeBuoy class="mr-2 h-4 w-4" />-->
      <!--        <span>Support</span>-->
      <!--      </DropdownMenuItem>-->
      <!--      <DropdownMenuItem disabled>-->
      <!--        <Cloud class="mr-2 h-4 w-4" />-->
      <!--        <span>API</span>-->
      <!--      </DropdownMenuItem>-->
      <!--      <DropdownMenuSeparator />-->

      <DropdownMenuItem @click="handleLogout">
        <LogOut class="mr-2 h-4 w-4" />
        <span>{{ t('common.loginOut') }}</span>
        <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
      </DropdownMenuItem>
      <!--      <AlertDialog>-->
      <!--        <AlertDialogTrigger as-child>-->
      <!--          -->
      <!--        </AlertDialogTrigger>-->
      <!--        <AlertDialogContent>-->
      <!--          <AlertDialogHeader>-->
      <!--            <AlertDialogTitle>{{t('common.reminder')}}</AlertDialogTitle>-->
      <!--            <AlertDialogDescription>-->
      <!--              {{t('common.loginOutMessage')}}-->
      <!--            </AlertDialogDescription>-->
      <!--          </AlertDialogHeader>-->
      <!--          <AlertDialogFooter>-->
      <!--            <AlertDialogCancel>{{t('common.cancel')}}</AlertDialogCancel>-->
      <!--            <AlertDialogAction @click="loginOut">{{t('common.ok')}}</AlertDialogAction>-->
      <!--          </AlertDialogFooter>-->
      <!--        </AlertDialogContent>-->
      <!--      </AlertDialog>-->
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<style scoped lang="scss"></style>
