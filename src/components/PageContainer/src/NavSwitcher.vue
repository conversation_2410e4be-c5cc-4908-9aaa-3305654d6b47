<script lang="ts" setup>
import remaining from "@/router/modules/remaining";
const { replace, currentRoute } = useRouter()

// 根据当前路由获取当前激活的菜单
const activeRoute = ref<AppRouteRecordRaw>()
const centerRoutes = remaining.find((_route: AppRouteRecordRaw) => _route.name === 'center')?.children
activeRoute.value = centerRoutes?.find((_route: AppRouteRecordRaw) => currentRoute.value.fullPath.includes(_route.path))
</script>

<template>
  <SidebarMenu>
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <div
              class="flex items-center justify-center rounded-lg aspect-square size-8 bg-sidebar-primary text-sidebar-primary-foreground"
            >
              <Icon :name="activeRoute?.meta.icon" class="size-4"/>
            </div>
            <div class="grid flex-1 text-sm leading-tight text-left">
              <span class="font-semibold truncate">{{ activeRoute?.meta.title }}</span>
            </div>
            <Icon name="ChevronsUpDown" class="ml-auto" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
          align="start"
          side="bottom"
          :side-offset="4"
        >
          <DropdownMenuLabel class="text-xs text-muted-foreground">
            Menus
          </DropdownMenuLabel>
          <DropdownMenuItem
            v-for="(_route, index) in centerRoutes"
            :key="_route.name"
            class="gap-2 p-2"
            @click="replace(_route.path)"
          >
            <div class="flex items-center justify-center rounded-sm size-6">
              <Icon :name="_route.meta.icon" :size="20" class="shrink-0"/>
            </div>
            {{ _route.meta.title }}
            <DropdownMenuShortcut>⌘{{ index + 1 }}</DropdownMenuShortcut>
          </DropdownMenuItem>
<!--          <DropdownMenuSeparator />-->
<!--          <DropdownMenuItem class="gap-2 p-2">-->
<!--            <div class="flex items-center justify-center border rounded-md size-6 bg-background">-->
<!--              <Icon name="Plus" class="size-4" />-->
<!--            </div>-->
<!--            <div class="font-medium text-muted-foreground">-->
<!--              Add Menu-->
<!--            </div>-->
<!--          </DropdownMenuItem>-->
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
</template>
