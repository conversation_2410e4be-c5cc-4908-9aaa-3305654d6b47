<script setup lang="ts">
import { useSlots } from 'vue';

const slots = useSlots();
const hasSideSlot = !!slots.side;
</script>

<template>
  <container-wrap class="flex-1 grid lg:grid-cols-6">
    <div v-if="hasSideSlot">
      <!--侧边栏插槽-->
      <slot name="side" />
    </div>
    <!--内容区域-->
    <div :class="['flex-1', hasSideSlot ? 'col-span-4 lg:col-span-5' : 'col-span-6']">
      <!--内容区-->
      <div class="flex-1">
        <slot name="content" />
      </div>
    </div>
  </container-wrap>
</template>
