<script lang="ts" setup>
import { useSidebar } from '@/components/ui/sidebar/utils'
import { sidebarData } from '../data/sidebar-data'

import NavFooter from './NavFooter.vue'
import NavGroup from './NavGroup.vue'
import NavSwitcher from './NavSwitcher.vue'

import { useAppStoreWithOut } from '@/store/modules/app'

const { state } = useSidebar()
const appStore = useAppStoreWithOut()
</script>

<template>
  <Sidebar collapsible="icon" class="z-50" :variant="appStore.getMaximize ? 'sidebar' : 'floating'">
    <SidebarHeader>
      <NavSwitcher :teams="sidebarData.teams" />
    </SidebarHeader>

    <SidebarContent>
      <div :class="{ hidden: state === 'collapsed' }">
        <slot />
      </div>
    </SidebarContent>

    <SidebarFooter>
      <NavFooter :user="sidebarData.user" />
    </SidebarFooter>

    <SidebarRail />
  </Sidebar>
</template>

<style></style>
