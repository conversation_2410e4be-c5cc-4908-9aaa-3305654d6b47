<script setup lang="ts">
import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Sparkles,
  UserRoundCog
} from 'lucide-vue-next'

import avatarImg from '/avatars/00.jpg'
import { useDesign } from '@/hooks/web/useDesign'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useUserStore } from '@/store/modules/user'
import { useLockStore } from '@/store/modules/lock'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'

defineOptions({ name: 'NavFooter' })

const { wsCache } = useCache()
const { t } = useI18n()

const { push, replace } = useRouter()

const userStore = useUserStore()

const tagsViewStore = useTagsViewStore()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('user-info')

const avatar = computed(() => userStore.user.avatar || avatarImg)
const nickname = computed(() => userStore.user.nickname ?? 'Admin')
const email = computed(() => userStore.user.email ?? '<EMAIL>')
// 登录类型
const loginType = ref(0)
// 锁定屏幕
const lockStore = useLockStore()
const getIsLock = computed(() => lockStore.getLockInfo?.isLock ?? false)
const dialogVisible = ref<boolean>(false)
const lockScreen = () => {
  dialogVisible.value = true
}

const handleLogout = async () => {
  try {
    loginType.value = wsCache.get('loginType')
    if (loginType.value == 1 || loginType.value == 2) {
      await userStore.loginOut()
    } else if (loginType.value == 3) {
      await userStore.mfaLoginOut()
    } else {
    }
    tagsViewStore.delAllViews()
    replace('/login?redirect=/').then()
  } catch {}
}
const toProfile = async () => {
  push('/user/profile')
}
</script>

<template>
  <SidebarMenu>
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <Avatar class="w-8 h-8 rounded-lg">
              <AvatarImage :src="avatar" :alt="nickname" />
              <AvatarFallback class="rounded-lg"> CN </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-sm leading-tight text-left">
              <span class="font-semibold truncate">{{ nickname }}</span>
              <span class="text-xs truncate">{{ email }}</span>
            </div>
            <ChevronsUpDown class="ml-auto size-4" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
          side="bottom"
          align="end"
          :side-offset="4"
        >
          <DropdownMenuLabel class="p-0 font-normal">
            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
              <Avatar class="w-8 h-8 rounded-lg">
                <AvatarImage :src="avatar" :alt="nickname" />
                <AvatarFallback class="rounded-lg"> CN </AvatarFallback>
              </Avatar>
              <div class="grid flex-1 text-sm leading-tight text-left">
                <span class="font-semibold truncate">{{ nickname }}</span>
                <span class="text-xs truncate">{{ email }}</span>
              </div>
            </div>
          </DropdownMenuLabel>

          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem @click="$router.push('/billing/')">
              <Sparkles />
              Upgrade to Pro
            </DropdownMenuItem>
          </DropdownMenuGroup>

          <DropdownMenuSeparator />
          <!--          <DropdownMenuGroup>-->
          <!--            <DropdownMenuItem @click="$router.push('/billing?type=billing')">-->
          <!--              <CreditCard />-->
          <!--              Billing-->
          <!--            </DropdownMenuItem>-->
          <!--          </DropdownMenuGroup>-->

          <!--          <DropdownMenuSeparator />-->
          <DropdownMenuGroup>
            <DropdownMenuItem @click="$router.push('/settings/')">
              <UserRoundCog />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem @click="$router.push('/settings/account')">
              <BadgeCheck />
              Account
            </DropdownMenuItem>
            <DropdownMenuItem @click="$router.push('/settings/notifications')">
              <Bell />
              Notifications
              <Badge variant="destructive" class="ml-auto"> 5 </Badge>
            </DropdownMenuItem>
          </DropdownMenuGroup>

          <DropdownMenuSeparator />
          <DropdownMenuItem @click="handleLogout">
            <LogOut />
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
</template>
