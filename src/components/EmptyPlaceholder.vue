<script setup lang="ts">
import noData from '@/assets/imgs/noData.png'
const slots = useSlots()
// 设置border传参
defineProps({
  border: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'No content added'
  },
  content: {
    type: String,
    default: 'You have not added any content. Add one now.'
  }
})
</script>

<template>
  <div
    class="flex h-[450px] shrink-0 items-center justify-center rounded-md"
    :class="{ 'border border-dashed': border }"
  >
    <div class="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
<!--      <svg-->
<!--        xmlns="http://www.w3.org/2000/svg"-->
<!--        fill="none"-->
<!--        stroke="currentColor"-->
<!--        strokeLinecap="round"-->
<!--        strokeLinejoin="round"-->
<!--        strokeWidth="2"-->
<!--        class="w-10 h-10 text-muted-foreground"-->
<!--        viewBox="0 0 24 24"-->
<!--      >-->
<!--        <circle cx="12" cy="11" r="1" />-->
<!--        <path-->
<!--          d="M11 17a1 1 0 0 1 2 0c0 .5-.34 3-.5 4.5a.5.5 0 0 1-1 0c-.16-1.5-.5-4-.5-4.5ZM8 14a5 5 0 1 1 8 0"-->
<!--        />-->
<!--        <path d="M17 18.5a9 9 0 1 0-10 0" />-->
<!--      </svg>-->
      <img :src="noData" class="max-w-[120px]"/>
      <slot v-if="slots.name" name="title" />
      <h3 v-else class="mt-4 text-lg font-semibold">
        {{ title }}
      </h3>

      <slot v-if="slots.description" name="description" />
      <p v-else class="mt-2 mb-4 text-sm text-muted-foreground">
        {{ content }}
      </p>
    </div>
    <slot name="action" />
  </div>
</template>
