<template>
  <div ref="container" class="typing-container break-words" />
</template>

<script setup lang="ts">
import MarkdownIt from "markdown-it";

const markdown = new MarkdownIt();

const props = defineProps<{
  message: string;
  speed?: number; // 打字速度，默认 30ms
  onType?: () => void; // 新增：每次打字后执行的回调
}>();

const emit = defineEmits<{
  (e: 'finish'): void;
}>();

const container = ref<HTMLElement | null>(null);
const renderedContent = ref<string>('');
let index = 0;
let timer: ReturnType<typeof setTimeout> | null = null;

// 边打字边渲染 Markdown 的逻辑
const typeWriter = () => {
  if (!container.value) return;

  if (index <= renderedContent.value.length) {
    const currentText = renderedContent.value.slice(0, index);
    // 实时渲染 Markdown 片段
    container.value.innerHTML = markdown.render(currentText + '_'); // 加个占位符模拟打字光标（可选）

    index++;
    timer = setTimeout(typeWriter, props.speed || 30);

    if (props.onType) {
      props.onType();
    }
  } else {
    // 完整渲染最终内容
    container.value.innerHTML = markdown.render(renderedContent.value);

    // 触发 finish 事件
    if (props.message.length > 0) {
      emit('finish');
    }
  }
};

// 监听 message 变化并重新渲染
watch(
  () => props.message,
  (newMessage) => {
    if (timer) clearTimeout(timer);

    if (!newMessage) {
      index = 0;
      renderedContent.value = '';
      if (container.value) container.value.innerHTML = '';
    } else {
      renderedContent.value = newMessage;
      if (index < renderedContent.value.length && container.value) {
        timer = setTimeout(typeWriter, props.speed || 30);
      }
    }
  },
  { immediate: true }
);

onMounted(() => {
  renderedContent.value = props.message;
  if (renderedContent.value && container.value) {
    timer = setTimeout(typeWriter, props.speed || 30);
  }
});

onBeforeUnmount(() => {
  if (timer) clearTimeout(timer);
});
</script>

<style scoped>
.typing-container {
  line-height: 1.5;
}
</style>
