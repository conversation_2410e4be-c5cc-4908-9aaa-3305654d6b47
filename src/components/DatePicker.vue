<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Calendar } from '@/components/ui/calendar'
import { SidebarGroup, SidebarGroupContent } from '@/components/ui/sidebar'
import dayjs, { type Dayjs } from 'dayjs'
import { CalendarDate, type DateValue, getLocalTimeZone } from '@internationalized/date'

interface Props {
  selectedDate?: Dayjs
}

const props = withDefaults(defineProps<Props>(), {
  selectedDate: () => dayjs() // 默认选中今天
})

const emit = defineEmits<{
  'date-select': [date: Dayjs]
}>()

// 内部选中日期状态
const internalSelectedDate = ref(props.selectedDate)

// 转换为 Calendar 组件需要的 DateValue 格式
const calendarValue = computed(() => {
  const date = internalSelectedDate.value
  return new CalendarDate(date.year(), date.month() + 1, date.date())
})

// 监听外部 selectedDate 变化
watch(() => props.selectedDate, (newDate) => {
  if (newDate && !newDate.isSame(internalSelectedDate.value, 'day')) {
    console.log('DatePicker: 外部日期变化', newDate.format('YYYY-MM-DD'))
    internalSelectedDate.value = newDate
  }
}, { immediate: true })

const handleDateSelect = (dateValue: DateValue | undefined) => {
  if (dateValue) {
    // 转换 DateValue 为 dayjs 对象
    const jsDate = dateValue.toDate(getLocalTimeZone())
    const dayjsDate = dayjs(jsDate)
    internalSelectedDate.value = dayjsDate
    emit('date-select', dayjsDate)
  }
}
</script>

<template>
  <SidebarGroup class="px-0">
    <SidebarGroupContent>
      <Calendar
        :model-value="calendarValue"
        class="[&_[role=gridcell].bg-accent]:bg-sidebar-primary [&_[role=gridcell].bg-accent]:text-sidebar-primary-foreground [&_[role=gridcell]]:w-[33px]"
        @update:model-value="handleDateSelect"
      />
    </SidebarGroupContent>
  </SidebarGroup>
</template>