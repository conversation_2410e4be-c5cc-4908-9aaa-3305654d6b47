<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'

defineOptions({ name: 'Error' })

interface ErrorMap {
  code: string
  title: string
  message: string
  buttonText: string
}

const { t } = useI18n()
const router = useRouter()
const errorMap: {
  [key: string]: ErrorMap
} = {
  '000': {
    code: '000',
    title: 'CONSTRUCTION',
    message: t('error.construction'),
    buttonText: t('error.returnToHome')
  },
  '404': {
    code: '404',
    title: 'PAGE NOT FOUND',
    message: t('error.pageError'),
    buttonText: t('error.returnToHome')
  },
  '500': {
    code: '500',
    title: 'NETWORK ERROR',
    message: t('error.networkError'),
    buttonText: t('error.returnToHome')
  },
  '403': {
    code: '403',
    title: 'NO PERMISSION',
    message: t('error.noPermission'),
    buttonText: t('error.returnToHome')
  }
}

const props = defineProps({
  type: propTypes.string.validate((v: string) => ['404', '500', '403'].includes(v)).def('404')
})

</script>

<template>
  <div class="VPContent">
    <div class="NotFound"><p class="code">{{ errorMap[type].code }}</p>
      <h1 class="title">{{ errorMap[type].title }}</h1>
      <div class="divider"></div>
      <blockquote class="quote">{{ errorMap[type].message }} </blockquote>
      <div class="action">
        <Button variant="outline" @click="router.push('/')">{{ errorMap[type].buttonText }}</Button>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.VPContent {
  flex-grow: 1;
  flex-shrink: 0;
  margin: auto 0;
  width: 100%;

  @media (min-width: 768px) {
    .NotFound {
      padding: 96px 32px 168px;
    }
  }

  .NotFound {
    padding: 64px 24px 96px;
    text-align: center;
    .code {
      line-height: 64px;
      font-size: 64px;
      font-weight: 600;
    }
    .title {
      padding-top: 12px;
      letter-spacing: 2px;
      line-height: 20px;
      font-size: 20px;
      font-weight: 700;
    }
    .divider {
      margin: 24px auto 18px;
      width: 64px;
      height: 1px;
      background-color: #e2e2e3;
    }
    .quote {
      margin: 0 auto;
      max-width: 256px;
      font-size: 14px;
      font-weight: 500;
      color: #67676c;
    }
    .action {
      padding-top: 20px;
    }
  }
}
</style>
