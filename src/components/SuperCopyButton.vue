<script setup lang="ts">
const props = withDefaults(defineProps<{
  content?: string
}>(), {
  content: '',
})
const { content } = toRefs(props)

const { copy, copied } = useClipboard({ source: content })
</script>

<template>
<!--  <Tooltip :delay-duration="100">-->
<!--    <TooltipTrigger as-child>-->
<!--      <Button-->
<!--        size="icon"-->
<!--        variant="ghost"-->
<!--        @click="copy()"-->
<!--      >-->
<!--        <span class="sr-only">Copy</span>-->
<!--        <Icon name="Check" v-if="copied" class="w-4 h-4"/>-->
<!--        <Icon name="Copy" v-else class="w-4 h-4"/>-->
<!--      </Button>-->
<!--    </TooltipTrigger>-->
<!--    <TooltipContent>Copy</TooltipContent>-->
<!--  </Tooltip>-->

  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger as-child>
        <Button
          size="icon"
          variant="ghost"
          @click="copy()"
        >
          <span class="sr-only">Copy</span>
          <Icon name="Check" v-if="copied" class="w-4 h-4"/>
          <Icon name="Copy" v-else class="w-4 h-4"/>
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        Copy
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
</template>
