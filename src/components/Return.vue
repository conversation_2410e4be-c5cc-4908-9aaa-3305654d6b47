<script setup lang="ts">
import { useRouter } from 'vue-router'

defineOptions({ name: 'ReturnHeader' })

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  backTo: {
    type: String,
    default: ''
  }
})

const router = useRouter()

const handleBack = () => {
  if (props.backTo) {
    router.push(props.backTo)
  } else {
    router.back()
  }
}
</script>

<template>
  <div class="flex items-center gap-2 px-5 py-3 ml-[-10px]">
    <Button
      variant="ghost"
      size="icon"
      @click="handleBack"
      class="flex items-center justify-center rounded-full cursor-pointer"
    >
      <Icon name="ArrowLeft" :size="20" class="text-primary" />
    </Button>
    <h1 class="text-xl font-semibold tracking-tight">{{ title }}</h1>
  </div>
</template>