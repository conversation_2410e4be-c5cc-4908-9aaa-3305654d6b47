<template>
  <div :class="['fixed z-30', computedPosition]" ref="wrapperRef">
    <transition-group
      enter-active-class="transition-all duration-200 ease-out"
      leave-active-class="transition-all duration-200 ease-in"
      enter-from-class="transform scale-50 translate-y-4 opacity-0"
      enter-to-class="transform scale-100 translate-y-0 opacity-100"
      leave-from-class="transform scale-100 translate-y-0 opacity-100"
      leave-to-class="transform scale-50 translate-y-4 opacity-0"
      tag="ul"
      class="flex flex-col items-center mb-2 space-y-2"
    >
      <li v-for="action in actions" v-show="isOpen" :key="action.name">
        <Button size="icon" class="rounded-full w-12 h-12 flex flex-col justify-center items-center" @click="action.handler">
          <Icon :name="action.icon" :size="30" style="width: 24px; height: 24px" />
        </Button>
      </li>
    </transition-group>
    <button
      class="flex items-center justify-center w-12 h-12 bg-gray-800 text-white rounded-full shadow-md transition-transform transform hover:scale-105"
      @click="toggleFab"
    >
      <Plus
        class="w-8 h-8 transition-transform duration-300"
        :class="{ 'rotate-135': isOpen }"
      ></Plus>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { onClickOutside } from '@vueuse/core';
import { Plus} from 'lucide-vue-next';

interface Action {
  name: string
  icon: string
  handler: () => void
}

interface Props {
  actions: Action[];
}

const props = defineProps<Props>();

const router = useRouter()

const isOpen = ref(false);
const wrapperRef = ref(null);

const options = {
  position: 'bottom-right',
};

const { position } = options;

const toggleFab = () => {
  isOpen.value = !isOpen.value;
};

onClickOutside(wrapperRef, () => {
  isOpen.value = false;
});

const positionClasses = {
  'bottom-right': 'bottom-4 right-4',
  'bottom-left': 'bottom-4 left-4',
  'top-right': 'top-4 right-4',
  'top-left': 'top-4 left-4',
};

const computedPosition = computed(() => positionClasses[position]);

const handleShowBot = () => {

}

</script>

<style>
.rotate-135 {
  transform: rotate(135deg);
}
</style>
