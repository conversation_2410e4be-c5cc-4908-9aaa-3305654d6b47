import { Layout } from '@/utils/routerHelper'
import { RouterView } from 'vue-router'

const { t } = useI18n()
/**
 * redirect: noredirect        当设置 noredirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'          设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
 hidden: true              当设置 true 的时候该路由不会再侧边栏出现 如404，login等页面(默认 false)

 alwaysShow: true          当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
 只有一个时，会将那个子路由当做根路由显示在侧边栏，
 若你想不管路由下面的 children 声明的个数都显示你的根路由，
 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
 一直显示根路由(默认 false)

 title: 'title'            设置该路由在侧边栏和面包屑中展示的名字

 icon: 'svg-name'          设置该路由的图标

 noCache: true             如果设置为true，则不会被 <keep-alive> 缓存(默认 false)

 breadcrumb: false         如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)

 affix: true               如果设置为true，则会一直固定在tag项中(默认 false)

 noTagsView: true          如果设置为true，则不会出现在tag中(默认 false)

 activeMenu: '/dashboard'  显示高亮的路由路径

 followAuth: '/dashboard'  跟随哪个路由进行权限过滤

 canTo: true               设置为true即使hidden为true，也依然可以进行路由跳转(默认 false)
 }
 **/
const remainingRouter: AppRouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    name: 'RedirectLayout',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  // {
  //   path: '/v3/callback',
  //   name: 'authing',
  //   meta: {},
  //   component: () => import('@/views/Login/components/Authing.vue')
  // },

  // 主布局 - 包含所有页面
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    name: 'Layout',
    meta: {},
    children: [
      {
        path: 'home',
        component: () => import('@/views/home/<USER>'),
        name: 'Home',
        meta: {
          title: t('router.home'),
          icon: 'ep:home-filled',
          noCache: false,
          affix: true
        }
      },
      //   path: '/ai',
      //   component: AILayout,
      //   redirect: '/ai/index',
      //   name: 'aiHome',
      //   meta: {},
      //   children: [
      //     {
      //       path: 'index',
      //       component: () => import('@/views/ai/Home/index.vue'),
      //       name: 'Index',
      //       meta: {
      //         title: t('router.home'),
      //         icon: 'ep:home-filled',
      //         noCache: false,
      //         affix: true
      //       }
      //     },
      //     {
      //       path: 'ocr',
      //       component: () => import('@/views/ai/ocr/index.vue'),
      //       name: 'OCR',
      //       meta: {
      //         title: t('router.home'),
      //         icon: 'ep:home-filled',
      //         noCache: false,
      //         affix: true
      //       }
      //     },
      //     {
      //       path: 'translate',
      //       component: () => import('@/views/ai/translate/index.vue'),
      //       name: 'Translate',
      //       meta: {
      //         title: t('router.home'),
      //         icon: 'ep:home-filled',
      //         noCache: false,
      //         affix: true
      //       }
      //     },
      //     {
      //       path: 'asr',
      //       component: () => import('@/views/ai/asr/index.vue'),
      //       name: 'ASR',
      //       meta: {
      //         title: t('router.home'),
      //         icon: 'ep:home-filled',
      //         noCache: false,
      //         affix: true
      //       }
      //     },
      //     {
      //       path: 'test',
      //       component: () => import('@/views/ai/Home/Test.vue'),
      //       name: 'test',
      //       meta: {
      //         title: t('Test'),
      //         icon: 'ep:home-filled',
      //         noCache: false,
      //         affix: true
      //       }
      //     }
      //   ]
      // },
      {
        path: '/edp',
        redirect: '/edp/career-develop-map',
        name: 'EmployeeDevelopment',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          title: 'Live Stream'
        },
        children: [
          {
            path: 'career-develop-map',
            component: () => import('@/views/edp/careerdevelopmap/index.vue'),
            name: 'CareerDevelopmentMap',
            meta: {
              title: 'Career Development Map',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'learning-map',
            component: () => import('@/views/edp/learningmap/index.vue'),
            name: 'LearningMap',
            meta: {
              title: 'Learning Map',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'study-plan',
            component: () => import('@/views/edp/studyplan/index.vue'),
            name: 'MyStudyPlan',
            meta: {
              title: 'My Study Plan',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'training-need',
            component: () => import('@/views/edp/trainingneed/index.vue'),
            name: 'TrainingNeed',
            meta: {
              title: 'Training Need',
              noCache: false,
              affix: true
            },
            children: [
              {
                path: 'add',
                component: () => import('@/views/edp/trainingneed/AddTrainingNeed.vue'),
                name: 'AddTrainingNeed',
                meta: {
                  title: 'Add Training Need',
                  noCache: false,
                  affix: true
                }
              },
              {
                path: 'edit/:id',
                component: () => import('@/views/edp/trainingneed/AddTrainingNeed.vue'),
                name: 'EditTrainingNeed',
                meta: {
                  title: 'Edit Training Need',
                  noCache: false,
                  affix: true
                }
              }
            ]
          }
        ]
      },
      {
        path: '/live',
        component: RouterView,
        redirect: '/live/center',
        name: 'Live',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Live Stream'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/live/center/index.vue'),
            name: 'LiveCenter',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id?',
            component: () => import('@/views/live/detail/index.vue'),
            name: 'LiveDetail',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      {
        path: '/ai',
        component: RouterView,
        redirect: '/ai/center',
        name: 'AI',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'AI Tools'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/ai/center/index.vue'),
            name: 'AICenter',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'asr/:id?',
            component: () => import('@/views/ai/asr/index.vue'),
            name: 'ASR',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'ocr/:id?',
            component: () => import('@/views/ai/ocr/index.vue'),
            name: 'OCR',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'translate/:id?',
            component: () => import('@/views/ai/translate/index.vue'),
            name: 'Translate',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      {
        path: '/content',
        component: RouterView,
        redirect: '/content/center',
        name: 'Content',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Content'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/content/index.vue'),
            name: 'ContentCenter',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id',
            component: () => import('@/views/content/detail/index.vue'),
            name: 'ContentDetail',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id/play/:chapterId',
            component: () => import('@/views/content/detail/play/index.vue'),
            name: 'ContentDetailPlay',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },

      {
        path: '/my-todo/todocourse/detail/:id/play/:chapterId',
        component: () => import('@/views/content/detail/play/index.vue'),
        name: 'TodoContentDetailPlay',
        meta: {
          breadcrumbs: [
            {
              url: '/index',
              text: () => t('menu.todocontent')
            },
            {
              url: '/my-todo/todocourse/detail/:id',
              text: () => t('breadcrumb.todocoursedetail'),
              param: [
                {
                  isParams: true,
                  keyName: 'id'
                }
              ]
            },
            {
              url: '/my-todo/todocourse/detail/:id/play/:chapterId',
              text: () => t('breadcrumb.coursedetailPlay'),
              param: [
                {
                  isParams: true,
                  keyName: 'id'
                },
                {
                  isParams: true,
                  keyName: 'chapterId'
                }
              ]
            }
          ]
          // activeMenu: '/index',
        }
      },
      {
        path: '/content/detail/:id/play/:chapterId',
        component: () => import('@/views/content/detail/play/index.vue'),
        name: 'ContentDetailPlay',
        meta: {
          breadcrumbs: [
            {
              url: '/content',
              text: () => t('menu.content')
            },
            {
              url: '/content/detail/:id',
              text: () => t('breadcrumb.coursedetail'),
              param: [
                {
                  isParams: true,
                  keyName: 'id'
                }
              ]
            },
            {
              url: '/content/detail/:id/play/:chapterId',
              text: () => t('breadcrumb.coursedetailPlay'),
              param: [
                {
                  isParams: true,
                  keyName: 'id'
                },
                {
                  isParams: true,
                  keyName: 'chapterId'
                }
              ]
            }
          ]
          // activeMenu: '/content',
        }
      },

      {
        path: '/training',
        component: RouterView,
        redirect: '/training/list',
        name: 'Training',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Training'
        },
        children: [
          {
            path: 'list',
            component: () => import('@/views/mlctraining/index.vue'),
            name: 'MLCTraining',
            meta: {
              title: 'MLCTraining',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail',
            component: () => import('@/views/mlctraining/Detail/index.vue'),
            name: 'TrainingDetail',
            meta: {
              title: 'Detail',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      {
        path: '/journey',
        component: RouterView,
        redirect: '/journey/center',
        name: 'Journey',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Journey'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/journey/index.vue'),
            name: 'JourneyCenter',
            meta: {
              title: 'JourneyCenter',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id',
            component: () => import('@/views/journey/detail/index.vue'),
            name: 'JourneyDetail',
            meta: {
              title: 'Journey Detail',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      {
        path: '/news',
        component: RouterView,
        redirect: '/news/center',
        name: 'News',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'News'
        },
        children: [
          {
            path: 'detail/:newsId(\\d+)',
            component: () => import('@/views/news/index.vue'),
            name: 'NewsCenter',
            meta: {
              title: 'News',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      {
        path: '/exam',
        component: RouterView,
        redirect: '/exam/center',
        name: 'Exam',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Exam'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/exam/index.vue'),
            name: 'ExamCenter',
            meta: {
              title: 'Exam',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'answer/:id',
            component: () => import('@/views/exam/answer/index.vue'),
            name: 'ExamAnswer',
            meta: {
              title: 'Exam Answer',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: ':courseId?/:recordId',
            component: () => import('@/views/exam/result/index.vue'),
            name: 'ExamResult',
            meta: {
              title: 'Exam Result',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'answer/:type/:courseId/:chapterId/:id',
            component: () => import('@/views/exam/answer/index.vue'),
            name: 'CourseExamAnswer',
            meta: {
              title: 'Exam Answer',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      {
        path: '/my-center',
        component: RouterView,
        name: 'MyCenter',
        redirect: '/my-center/index',
        meta: {
          title: 'My Center',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true
        },
        children: [
          {
            path: 'index',
            component: () => import('@/views/mycenter/index.vue'),
            name: 'MyCenterIndex',
            meta: {
              title: 'My Center',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id/play/:chapterId',
            component: () => import('@/views/content/detail/play/index.vue'),
            name: 'MyCenterContentDetailPlay',
            meta: {
              title: 'Course Play',
              icon: 'ep:video-play',
              noCache: false,
              affix: true,
              breadcrumbs: [
                {
                  url: '/my-center',
                  text: () => t('menu.mycenter')
                },
                {
                  url: '/my-center/detail/:id',
                  text: () => t('breadcrumb.coursedetail'),
                  param: [
                    {
                      isParams: true,
                      keyName: 'id'
                    }
                  ]
                },
                {
                  url: '/my-center/detail/:id/play/:chapterId',
                  text: () => t('breadcrumb.coursedetailPlay'),
                  param: [
                    {
                      isParams: true,
                      keyName: 'id'
                    },
                    {
                      isParams: true,
                      keyName: 'chapterId'
                    }
                  ]
                }
              ]
            }
          }
        ]
      },
      // onboarding 路由
      {
        path: '/onboarding',
        component: RouterView,
        redirect: '/onboarding/center',
        name: 'Onboarding',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Onboarding'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/onboarding/index.vue'),
            name: 'OnboardingCenter',
            meta: {
              title: 'Onboarding',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id',
            component: () => import('@/views/onboarding/detail/index.vue'),
            name: 'OnboardingDetail',
            meta: {
              title: 'Onboarding Detail',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      // orientation 路由
      {
        path: '/orientation',
        component: RouterView,
        redirect: '/orientation/center',
        name: 'Orientation',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Orientation'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/orientation/index.vue'),
            name: 'OrientationCenter',
            meta: {
              title: 'Orientation',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id',
            component: () => import('@/views/orientation/detail/index.vue'),
            name: 'OrientationDetail',
            meta: {
              title: 'Orientation Detail',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      // Onboarding Interactive Map 路由
      {
        path: '/onboarding-map',
        component: () => import('@/views/learning/onboardingmap/index.vue'),
        name: 'Onboarding Map',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Orientation'
        }
      },
      // survey 路由
      {
        path: '/survey',
        component: RouterView,
        redirect: '/survey/center',
        name: 'Survey',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'ClipboardList',
          title: 'Survey'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/survey/index.vue'),
            name: 'SurveyCenter',
            meta: {
              title: 'Survey Center',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id',
            component: () => import('@/views/survey/detail/index.vue'),
            name: 'SurveyDetail',
            meta: {
              title: 'Survey Detail',
              icon: 'ep:document',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'fill/:id',
            component: () => import('@/views/survey/fill/index.vue'),
            name: 'SurveyFill',
            meta: {
              title: 'Fill Survey',
              icon: 'ep:edit',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'result/:id',
            component: () => import('@/views/survey/result/index.vue'),
            name: 'SurveyResult',
            meta: {
              title: 'Survey Result',
              icon: 'ep:data-analysis',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'view/:responseId',
            component: () => import('@/views/survey/view/index.vue'),
            name: 'SurveyView',
            meta: {
              title: 'Survey View',
              icon: 'ep:view',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      // policy 路由
      {
        path: '/policy',
        component: RouterView,
        redirect: '/policy/center',
        name: 'CompanyPolicy',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Company Policy'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/policy/index.vue'),
            name: 'CompanyPolicyCenter',
            meta: {
              title: 'Company Policy',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id',
            component: () => import('@/views/policy/detail/index.vue'),
            name: 'CompanyPolicyDetail',
            meta: {
              title: 'Company Policy Detail',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      {
        path: '/message',
        component: RouterView,
        name: 'Message',
        redirect: '/message/list',
        meta: {
          title: 'Message',
          icon: 'ep:home-filled',
          noCache: false,
          affix: true
        },
        children: [
          {
            path: 'list',
            component: () => import('@/views/message/index.vue'),
            name: 'MessageList',
            meta: {
              title: 'Message List',
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      // Calendar 路由
      {
        path: '/calendar',
        component: () => import('@/views/calendar/index.vue'),
        name: 'Calendar',
        meta: {
          title: 'Calendar',
          icon: 'ep:calendar',
          noCache: false
        }
      }
    ]
  },

  // 子系统
  /*  {
    path: '/',
    component: RouterView,
    name: 'center',
    meta: {},
    children: [
      // 直播子系统
      {
        path: '/live',
        component: () => import('@/views/live/layout/layout.vue'),
        redirect: '/live/center',
        name: 'Live',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Youtube',
          title: 'Live Stream'
        },
        children: [
          {
            path: 'center',
            component: () => import('@/views/live/center/index.vue'),
            name: 'LiveCenter',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          },
          {
            path: 'detail/:id?',
            component: () => import('@/views/live/detail/index.vue'),
            name: 'LiveDetail',
            meta: {
              title: t('router.home'),
              icon: 'ep:home-filled',
              noCache: false,
              affix: true
            }
          }
        ]
      },
      // AI Toolkit子系统
      {
        path: '/ai',
        // component: Live,
        redirect: '/ai/center',
        name: 'AI',
        meta: {
          noCache: false,
          hidden: true,
          canTo: true,
          icon: 'Bot',
          title: 'AI Toolkit'
        }
      }
      // ...
    ]
  },*/
  {
    path: '/preview',
    component: () => import('@/views/preview/index.vue'),
    name: 'previewDetail',
    meta: {
      noCache: false,
      hidden: true,
      canTo: true,
      icon: 'ep:view',
      title: 'Preview'
    }
  },
  // {
  //   path: '/user',
  //   component: Layout,
  //   name: 'UserInfo',
  //   meta: {
  //     hidden: true
  //   },
  //   children: [
  //     {
  //       path: 'profile',
  //       component: () => import('@/views/Profile/Index.vue'),
  //       name: 'Profile',
  //       meta: {
  //         canTo: true,
  //         hidden: true,
  //         noTagsView: false,
  //         icon: 'ep:user',
  //         title: t('common.profile')
  //       }
  //     },
  //     {
  //       path: 'notify-message',
  //       component: () => import('@/views/system/notify/my/index.vue'),
  //       name: 'MyNotifyMessage',
  //       meta: {
  //         canTo: true,
  //         hidden: true,
  //         noTagsView: false,
  //         icon: 'ep:message',
  //         title: '我的站内信'
  //       }
  //     }
  //   ]
  // },
  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/sso',
    component: () => import('@/views/Login/Login.vue'),
    name: 'SSOLogin',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  // {
  //   path: '/social-login',
  //   component: () => import('@/views/Login/SocialLogin.vue'),
  //   name: 'SocialLogin',
  //   meta: {
  //     hidden: true,
  //     title: t('router.socialLogin'),
  //     noTagsView: true
  //   }
  // },
  // {
  //   path: '/construction',
  //   component: () => import('@/views/Error/construction.vue'),
  //   name: 'Construction',
  //   meta: {
  //     hidden: true,
  //     title: 'Construction',
  //     noTagsView: true
  //   }
  // },
  {
    path: '/403',
    component: () => import('@/views/Error/403.vue'),
    name: 'NoAccess',
    meta: {
      hidden: true,
      title: '403',
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFound',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/500',
    component: () => import('@/views/Error/500.vue'),
    name: 'Error',
    meta: {
      hidden: true,
      title: '500',
      noTagsView: true
    }
  }
  // {
  //   path: '/bpm',
  //   component: Layout,
  //   name: 'bpm',
  //   meta: {
  //     hidden: true
  //   },
  //   children: [
  //     {
  //       path: 'manager/form/edit',
  //       component: () => import('@/views/bpm/form/editor/index.vue'),
  //       name: 'BpmFormEditor',
  //       meta: {
  //         noCache: true,
  //         hidden: true,
  //         canTo: true,
  //         title: '设计流程表单',
  //         activeMenu: '/bpm/manager/form'
  //       }
  //     },
  //     {
  //       path: 'manager/model/edit',
  //       component: () => import('@/views/bpm/model/editor/index.vue'),
  //       name: 'BpmModelEditor',
  //       meta: {
  //         noCache: true,
  //         hidden: true,
  //         canTo: true,
  //         title: '设计流程',
  //         activeMenu: '/bpm/manager/model'
  //       }
  //     },
  //     {
  //       path: 'manager/simple/model',
  //       component: () => import('@/views/bpm/simple/SimpleModelDesign.vue'),
  //       name: 'SimpleModelDesign',
  //       meta: {
  //         noCache: true,
  //         hidden: true,
  //         canTo: true,
  //         title: '仿钉钉设计流程',
  //         activeMenu: '/bpm/manager/model'
  //       }
  //     },
  //     {
  //       path: 'manager/definition',
  //       component: () => import('@/views/bpm/definition/index.vue'),
  //       name: 'BpmProcessDefinition',
  //       meta: {
  //         noCache: true,
  //         hidden: true,
  //         canTo: true,
  //         title: '流程定义',
  //         activeMenu: '/bpm/manager/model'
  //       }
  //     },
  //     {
  //       path: 'process-instance/detail',
  //       component: () => import('@/views/bpm/processInstance/detail/index.vue'),
  //       name: 'BpmProcessInstanceDetail',
  //       meta: {
  //         noCache: true,
  //         hidden: true,
  //         canTo: true,
  //         title: '流程详情',
  //         activeMenu: '/bpm/task/my'
  //       },
  //       props: (route) => ({
  //         id: route.query.id,
  //         taskId: route.query.taskId,
  //         activityId: route.query.activityId
  //       })
  //     },
  //     {
  //       path: 'oa/leave/create',
  //       component: () => import('@/views/bpm/oa/leave/create.vue'),
  //       name: 'OALeaveCreate',
  //       meta: {
  //         noCache: true,
  //         hidden: true,
  //         canTo: true,
  //         title: '发起 OA 请假',
  //         activeMenu: '/bpm/oa/leave'
  //       }
  //     },
  //     {
  //       path: 'oa/leave/detail',
  //       component: () => import('@/views/bpm/oa/leave/detail.vue'),
  //       name: 'OALeaveDetail',
  //       meta: {
  //         noCache: true,
  //         hidden: true,
  //         canTo: true,
  //         title: '查看 OA 请假',
  //         activeMenu: '/bpm/oa/leave'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/member',
  //   component: Layout,
  //   name: 'MemberCenter',
  //   meta: { hidden: true },
  //   children: [
  //     {
  //       path: 'user/detail/:id',
  //       name: 'MemberUserDetail',
  //       meta: {
  //         title: '会员详情',
  //         noCache: true,
  //         hidden: true
  //       },
  //       component: () => import('@/views/member/user/detail/index.vue')
  //     }
  //   ]
  // },
  // {
  //   path: '/:pathMatch(.*)*',
  //   component: () => import('@/views/Error/404.vue'),
  //   name: '',
  //   meta: {
  //     title: '404',
  //     hidden: true,
  //     breadcrumb: false
  //   }
  // },
  // {
  //   path: '/iot',
  //   component: Layout,
  //   name: 'IOT',
  //   meta: {
  //     hidden: true
  //   },
  //   children: [
  //     {
  //       path: 'product/detail/:id',
  //       name: 'IoTProductDetail',
  //       meta: {
  //         title: '产品详情',
  //         noCache: true,
  //         hidden: true,
  //         activeMenu: '/iot/product'
  //       },
  //       component: () => import('@/views/iot/product/detail/index.vue')
  //     },
  //     {
  //       path: 'device/detail/:id',
  //       name: 'IoTDeviceDetail',
  //       meta: {
  //         title: '设备详情',
  //         noCache: true,
  //         hidden: true,
  //         activeMenu: '/iot/device'
  //       },
  //       component: () => import('@/views/iot/device/detail/index.vue')
  //     }
  //   ]
  // }
]

export default remainingRouter