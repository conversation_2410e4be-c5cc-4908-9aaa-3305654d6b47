<!--<script setup lang="ts">-->

<!--// import { Content, useData, useRoute, useRouter } from 'vitepress'-->
<!--// import CodeConfigCustomizer from '../components/CodeConfigCustomizer.vue'-->
<!--import { Header } from './components/Header'-->
<!--import { Footer } from './components/Footer'-->

<!--</script>-->

<!--<template>-->
<!--  <SuperMessage />-->
<!--  &lt;!&ndash;  <FabButton :actions="actions" />&ndash;&gt;-->

<!--  &lt;!&ndash;  <Toaster />&ndash;&gt;-->
<!--  &lt;!&ndash; <VueQueryDevtools /> &ndash;&gt;-->

<!--  <TooltipProvider>-->
<!--    <Header />-->
<!--    <main class="flex flex-1 flex-col">-->
<!--      <RouterView />-->
<!--    </main>-->
<!--    <Footer />-->
<!--  </TooltipProvider>-->
<!--</template>-->

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Header } from './components/Header'
import { Footer } from './components/Footer'

const route = useRoute()

// 检测是否为 Home 页面
const isHomePage = computed(() => route.name === 'Home')

// 检测是否需要Footer（仅Home页面）
const needsFooter = computed(() => isHomePage.value)

// 内容区域的类名和样式
const contentAreaClass = computed(() => {
  if (isHomePage.value) {
    // Home页面：flex布局，允许滚动
    return 'flex-1 flex flex-col overflow-auto'
  } else {
    // 其他页面：限制在中间区域，带padding
    return 'flex-1 flex p-4 space-x-4 overflow-hidden'
  }
})

const mainContentClass = computed(() => {
  if (isHomePage.value) {
    // Home页面：flex布局，带padding，不设置高度限制
    return 'flex p-4 space-x-4'
  } else {
    // 其他页面：中间区域布局
    return 'flex-1 mx-auto space-y-4 overflow-auto'
  }
})

const homeContentClass = computed(() => {
  // Home页面主内容区域
  return 'flex-1 space-y-4'
})
</script>

<template>
  <SidebarProvider class="h-full" :default-open="false" :open="false">
    <SidebarLeft collapsible="icon"/>
    <SidebarInset class="bg-muted h-full flex flex-col">
      <Header />
      <div :class="contentAreaClass">
        <!-- Home页面：整个区域滚动 -->
        <template v-if="isHomePage">
          <div :class="mainContentClass">
            <div :class="homeContentClass">
              <RouterView />
            </div>

            <!-- 右侧日历组件 -->
            <div class="hidden 2xl:block flex-shrink-0">
              <SidebarRight class="rounded-lg bg-white border shadow h-full" />
            </div>
          </div>

          <!-- Home页面的Footer横跨整个页面 -->
          <Footer />
        </template>

        <!-- 其他页面：中间区域布局 -->
        <template v-else>
          <div :class="mainContentClass">
            <RouterView />
          </div>
          <div class="hidden 2xl:block ms-4 flex-shrink-0">
            <SidebarRight class="rounded-lg bg-white border shadow h-full" />
          </div>
        </template>
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>

<style>
@reference "tailwindcss";

.container-wrapper {
  & > .container-wrap {
    &:last-of-type {
      @apply flex-1;
    }
  }
}
</style>
