<script setup lang="ts">
import router from '@/router'
import { useI18n } from 'vue-i18n'
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()

const { t } = useI18n()
const aboutusTeams = [
  {
    title: () => t('footer.aboutus.title'),
    icon: 'aboutus-w',
    children: [
      {
        text: () => t('footer.aboutus.company'),
        url: 'https://www.majnoon-ifms.com/',
        key: 'company'
      },
      {
        text: () => t('footer.aboutus.ourteam'),
        url: undefined,
        key: 'ourteam'
      },
      {
        text: () => t('footer.terms.faq'),
        url: undefined,
        key: 'faqs'
      }
    ]
  },
  {
    title: () => t('footer.terms.title'),
    icon: 'terms-w',
    children: [
      {
        text: () => t('footer.terms.termsofuse'),
        url: undefined,
        key: 'termsofuse'
      },
      {
        text: () => t('footer.terms.privacypolicy'),
        url: undefined,
        key: 'privacypolicy'
      },
      {
        text: () => t('footer.aboutus.bat'),
        url: undefined,
        key: 'becomeateacher'
      }
    ]
  }
]

function handleOpen(url: string) {
  window.open(url)
}

/** 点击下方链接跳转对应位置 **/
function handleAnchor(li: { text: () => string; url: string | undefined; key: string }) {
  if (li.key === 'company') window.open(li.url, '_blank')

  if (li.key === 'faqs') {
    router.push({ name: 'helpDoc', query: { pn: 'faqs' } })
  }
  if (li.key === 'privacypolicy') {
    router.push({ name: 'helpDoc', query: { pn: 'priva' } })
  }
  if (li.key === 'termsofuse') {
    router.push({ name: 'helpDoc', query: { pn: 'terms' } })
  }
  if (li.key === 'becomeateacher') {
    router.push({ name: 'helpDoc', query: { pn: 'become' } })
  }
}
</script>

<template>
  <footer :class="cn('w-full bg-[#1B7F5A] text-white py-8', props.class)">
    <div class="max-w-7xl mx-auto px-8">
      <div class="flex justify-start items-start gap-16">
        <!-- Logo 和描述 -->
        <div class="w-80">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
              <svg viewBox="0 0 24 24" class="w-5 h-5 text-[#1B7F5A]">
                <path
                  fill="currentColor"
                  d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z"
                />
              </svg>
            </div>
            <span class="text-lg font-semibold">Online Learning Platform</span>
          </div>
          <p class="text-sm text-white/90 leading-relaxed">
            {{ $t('footer.des') }}
          </p>
        </div>

        <!-- About Us -->
        <div class="w-48">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
              <svg viewBox="0 0 24 24" class="w-5 h-5 text-[#1B7F5A]">
                <path
                  fill="currentColor"
                  d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                />
              </svg>
            </div>
            <span class="text-lg font-semibold">About Us</span>
          </div>
          <ul class="space-y-1">
            <li v-for="(item, index) in aboutusTeams[0]?.children" :key="index">
              <a
                href="#"
                class="text-sm text-white/90 hover:text-white transition-colors cursor-pointer flex items-center"
                @click="handleAnchor(item)"
              >
                <span class="mr-2">></span>
                {{ item.text() }}
              </a>
            </li>
          </ul>
        </div>

        <!-- Terms -->
        <div class="w-48">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
              <svg viewBox="0 0 24 24" class="w-5 h-5 text-[#1B7F5A]">
                <path
                  fill="currentColor"
                  d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"
                />
              </svg>
            </div>
            <span class="text-lg font-semibold">Terms</span>
          </div>
          <ul class="space-y-1">
            <li v-for="(item, index) in aboutusTeams[1]?.children" :key="index">
              <a
                href="#"
                class="text-sm text-white/90 hover:text-white transition-colors cursor-pointer flex items-center"
                @click="handleAnchor(item)"
              >
                <span class="mr-2">></span>
                {{ item.text() }}
              </a>
            </li>
          </ul>
        </div>

        <!-- Contact Us -->
        <div class="w-64">
          <div class="flex items-center mb-4">
            <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
              <svg viewBox="0 0 24 24" class="w-5 h-5 text-[#1B7F5A]">
                <path
                  fill="currentColor"
                  d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"
                />
              </svg>
            </div>
            <span class="text-lg font-semibold">Contact Us</span>
          </div>
          <p class="text-sm text-white/90"> <EMAIL> </p>
        </div>
      </div>
    </div>
  </footer>
</template>
