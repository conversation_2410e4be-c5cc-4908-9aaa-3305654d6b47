<script setup lang="ts">
import {
  Home,
  BookOpen,
  Users,
  Calendar,
  Settings,
  User,
  FileText,
  GraduationCap,
  Building,
  Shield,
  Award,
  MessageSquare
} from 'lucide-vue-next'

interface INav {
  name: string
  href: string
  description?: string
  route: string
  icon?: any
  subNavs?: INav[]
}

const router = useRouter()
const route = useRoute()
const navList: INav[] = [
  {
    name: 'Home',
    href: '/',
    route: '/home',
    // icon: Home,
    description:
      'A modal dialog that interrupts the user with important content and expects a response.'
  },
  {
    name: 'Learning Center',
    href: '/courses',
    route: '/courses',
    // icon: BookOpen,
    description: 'For sighted users to preview content available behind a link.',
    subNavs: [
      {
        name: 'Courses',
        href: '/content/center',
        route: '/content/center',
        // icon: FileText,
        description:
          'Discover a wide array of resources and materials to enhance your learning experience and knowledge depth.'
      },
      {
        name: 'Journey',
        href: '/journey/center',
        route: '/journey/center',
        // icon: FileText,
        description:
          'Discover a wide array of resources and materials to enhance your learning experience and knowledge depth.'
      },
      {
        name: 'Onboarding Interactive Map',
        href: '/onboarding-map',
        route: '/onboarding-map',
        // icon: GraduationCap,
        description:
          'Get started with comprehensive guidance and support to ensure a smooth transition into your new role or platform.'
      },
      {
        name: 'Orientation',
        href: '/orientation',
        route: '/orientation',
        // icon: Building,
        description:
          'Navigate through essential information and tools designed to acquaint you with our system and environment.'
      },
      {
        name: 'Company Policy',
        href: '/policy',
        route: '/policy',
        // icon: Shield,
        description:
          'Access key guidelines and regulations that govern workplace conduct and organizational procedures.'
      }
    ]
  },
  {
    name: 'MLC Training',
    href: '/training/list',
    route: '/training/list',
    // icon: Award,
    description: 'For sighted users to preview content available behind a link.'
  },
  {
    name: 'EDP',
    href: '/edp',
    route: '/edp',
    // icon: Users,
    description: 'For sighted users to preview content available behind a link.',
    subNavs: [
      {
        name: 'Career Development Map',
        href: '/edp/career-develop-map',
        route: '/edp/career-develop-map',
        description:
          'Offers a personalized roadmap for career development, helping you plan your career and achieve long-term goals.'
      },
      {
        name: 'Learning Map',
        href: '/edp/learning-map',
        route: '/edp/learning-map',
        description:
          'Customize learning paths and track your progress for a clear understanding of key points throughout your learning journey.'
      },
      {
        name: 'Knowledge Management',
        href: '/edp/knowledge',
        route: '/edp/knowledge',
        description:
          'Provides tools for effectively organizing, storing, and sharing information to improve work efficiency.'
      },
      // {
      //   name: 'My Study Plan',
      //   href: '/edp/study-plan',
      //   route: '/edp/study-plan',
      //   description:
      //     'Develop a personalized study plan to efficiently arrange your study time and achieve learning objectives.'
      // },
      {
        name: 'Training Need',
        href: '/edp/training-need',
        route: '/edp/training-need',
        description:
          'Develop a personalized study plan to efficiently arrange your study time and achieve learning objectives.'
      },
      {
        name: 'My Study Report',
        href: '/edp/study-report',
        route: '/edp/study-report',
        description:
          'Provides study reports that analyze your performance and progress, helping you continuously optimize your learning strategies.'
      }
    ]
  },
  {
    name: 'Live Stream',
    href: '/live?type=stream-central',
    route: '/live',
    // icon: Calendar,
    description:
      'Displays an indicator showing the completion progress of a task, typically displayed as a progress bar.'
  },
  {
    name: 'My Center',
    href: '/my-center',
    route: '/my-center',
    // icon: User,
    description: 'For sighted users to preview content available behind a link.'
    // subNavs: [
    //   {
    //     name: 'My Notebook',
    //     href: '/my/notebook',
    //     route: '/my/notebook',
    //     description: 'For sighted users to preview content available behind a link.'
    //   },
    //   {
    //     name: 'My Exam',
    //     href: '/my/exam',
    //     route: '/my/exam',
    //     description: 'For sighted users to preview content available behind a link.'
    //   },
    //   {
    //     name: 'My Certification',
    //     href: '/my/certification',
    //     route: '/my/certification',
    //     description: 'For sighted users to preview content available behind a link.'
    //   },
    // ]
  }
]
</script>

<template>
  <NavigationMenu>
    <NavigationMenuList class="flex-wrap 2xl:flex-nowrap">
      <template v-for="nav of navList" :key="nav.name">
        <NavigationMenuItem v-if="!(nav.subNavs && nav.subNavs.length > 0)">
          <Button
            variant="ghost"
            @click="router.push(nav.href)"
            :class="{ 'text-primary': route.fullPath.includes(nav.route) }"
            class="hover:text-primary text-xs 2xl:text-sm px-2 py-1 h-7 2xl:h-9 2xl:px-3 2xl:py-2 flex items-center gap-1 2xl:gap-2"
          >
            <component v-if="nav.icon" :is="nav.icon" class="w-3 h-3 2xl:w-4 2xl:h-4" />
            <span>{{ nav.name }}</span>
          </Button>
        </NavigationMenuItem>
        <NavigationMenuItem v-else>
          <NavigationMenuTrigger
            :class="{ 'text-primary': route.fullPath.includes(nav.route) }"
            class="hover:text-primary text-xs 2xl:text-sm px-2 py-1 h-7 2xl:h-9 2xl:px-3 2xl:py-2 flex items-center gap-1 2xl:gap-2"
          >
            <component v-if="nav.icon" :is="nav.icon" class="w-3 h-3 2xl:w-4 2xl:h-4" />
            <span>{{ nav.name }}</span>
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul
              class="grid w-[280px] gap-2 p-3 2xl:w-[400px] 2xl:gap-3 2xl:p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]"
            >
              <li v-for="subNav in nav.subNavs" :key="subNav.name" class="group">
                <NavigationMenuLink as-child>
                  <div
                    @click="router.push(subNav.href)"
                    class="block p-2 2xl:p-3 space-y-1 leading-none no-underline transition-colors rounded-md outline-hidden select-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                  >
                    <div
                      class="text-xs 2xl:text-sm font-medium leading-none group-hover:text-primary flex items-center gap-2"
                      :class="{ 'text-primary': route.fullPath.includes(subNav.route) }"
                    >
                      <component
                        v-if="subNav.icon"
                        :is="subNav.icon"
                        class="w-3 h-3 2xl:w-4 2xl:h-4"
                      />
                      <span>{{ subNav.name }}</span>
                    </div>
                    <p v-if="subNav.description" class="text-xs 2xl:text-sm leading-snug line-clamp-2 text-muted-foreground">
                      {{ subNav.description }}
                    </p>
                  </div>
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </template>

      <!--      <NavigationMenuItem>-->
      <!--        <NavigationMenuTrigger>Components</NavigationMenuTrigger>-->
      <!--        <NavigationMenuContent>-->
      <!--          <ul class="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px] ">-->
      <!--            <li v-for="component in components" :key="component.title">-->
      <!--              <NavigationMenuLink as-child>-->
      <!--                <a-->
      <!--                  :href="component.href"-->
      <!--                  class="block p-3 space-y-1 leading-none no-underline transition-colors rounded-md outline-hidden select-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"-->
      <!--                >-->
      <!--                  <div class="text-sm font-medium leading-none">{{ component.title }}</div>-->
      <!--                  <p class="text-sm leading-snug line-clamp-2 text-muted-foreground">-->
      <!--                    {{ component.description }}-->
      <!--                  </p>-->
      <!--                </a>-->
      <!--              </NavigationMenuLink>-->
      <!--            </li>-->
      <!--          </ul>-->
      <!--        </NavigationMenuContent>-->
      <!--      </NavigationMenuItem>-->

      <!--      <NavigationMenuItem>-->
      <!--        <NavigationMenuTrigger>Learning</NavigationMenuTrigger>-->
      <!--        <NavigationMenuContent>-->
      <!--          <ul-->
      <!--            class="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[minmax(0,.75fr)_minmax(0,1fr)]"-->
      <!--          >-->
      <!--            <li class="row-span-3">-->
      <!--              <NavigationMenuLink as-child>-->
      <!--                <div-->
      <!--                  class="flex flex-col justify-end w-full h-full p-6 no-underline rounded-md outline-hidden cursor-pointer select-none bg-linear-to-b from-muted/50 to-muted focus:shadow-md"-->
      <!--                  @click="router.push('/learning/Onboarding')"-->
      <!--                >-->
      <!--                  <div class="mt-4 mb-2 text-lg font-medium"> Onboarding </div>-->
      <!--                  <p class="text-sm leading-tight text-muted-foreground">-->
      <!--                    Guide to familiarize yourself with company processes and workflows.-->
      <!--                  </p>-->
      <!--                </div>-->
      <!--              </NavigationMenuLink>-->
      <!--            </li>-->

      <!--            <li>-->
      <!--              <NavigationMenuLink as-child>-->
      <!--                <div-->
      <!--                  @click="router.push('/learning/Orientation')"-->
      <!--                  class="block p-3 space-y-1 leading-none no-underline transition-colors rounded-md outline-hidden cursor-pointer select-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"-->
      <!--                >-->
      <!--                  <div class="text-sm font-medium leading-none">Orientation</div>-->
      <!--                  <p class="text-sm leading-snug line-clamp-2 text-muted-foreground">-->
      <!--                    Learn your role’s responsibilities and key tasks through role-specific training.-->
      <!--                  </p>-->
      <!--                </div>-->
      <!--              </NavigationMenuLink>-->
      <!--            </li>-->
      <!--            <li>-->
      <!--              <NavigationMenuLink as-child>-->
      <!--                <div-->
      <!--                  @click="router.push('/learning/Policy')"-->
      <!--                  class="block p-3 space-y-1 leading-none no-underline transition-colors rounded-md outline-hidden cursor-pointer select-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"-->
      <!--                >-->
      <!--                  <div class="text-sm font-medium leading-none">Company Policy</div>-->
      <!--                  <p class="text-sm leading-snug line-clamp-2 text-muted-foreground">-->
      <!--                    Overview of company policies, compliance guidelines, and workplace expectations.-->
      <!--                  </p>-->
      <!--                </div>-->
      <!--              </NavigationMenuLink>-->
      <!--            </li>-->
      <!--          </ul>-->
      <!--        </NavigationMenuContent>-->
      <!--      </NavigationMenuItem>-->

      <!--      <NavigationMenuItem>-->
      <!--        <Button variant="ghost" @click="router.push('/ai')">Toolkit</Button>-->
      <!--      </NavigationMenuItem>-->

      <!--      <NavigationMenuItem>-->
      <!--        <Button variant="ghost" @click="router.push('/live/center?type=stream-central')"-->
      <!--          >Live Stream-->
      <!--        </Button>-->
      <!--      </NavigationMenuItem>-->

      <!--      <NavigationMenuItem>-->
      <!--        <Button variant="ghost" @click="router.push('/my')">My Center</Button>-->
      <!--      </NavigationMenuItem>-->
    </NavigationMenuList>
  </NavigationMenu>
</template>
