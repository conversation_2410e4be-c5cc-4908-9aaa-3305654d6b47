<script setup lang="ts">
import { cn } from '@/lib/utils'
import { useConfigStore } from '@/store/modules/config'
import { useMagicKeys, useToggle } from '@vueuse/core'

import { onMounted, ref, watch } from 'vue'
import MoonIcon from '~icons/lucide/moon'
import SunIcon from '~icons/lucide/sun'
import Circle from '~icons/radix-icons/circle'
import File from '~icons/radix-icons/file'

defineOptions({ name: 'Header' })

import { docsConfig } from '@/config/docs'
import { Navigation } from '@/layout/components/Navigation'

const { radius, theme } = useConfigStore()
// Whenever the component is mounted, update the document class list
onMounted(() => {
  document.documentElement.style.setProperty('--radius', `${radius.value}rem`)
  document.documentElement.classList.add(`theme-${theme.value}`)
})

const route = useRoute()
const router = useRouter()

const links = [
  // {
  //   name: 'GitHub',
  //   href: 'https://github.com/unovue/shadcn-vue',
  //   icon: GithubLogoIcon,
  // },
]

const isOpen = ref(false)
const { Meta_K, Ctrl_K } = useMagicKeys({
  passive: false,
  onEventFired(e) {
    if (e.key === 'k' && (e.metaKey || e.ctrlKey)) e.preventDefault()
  }
})

watch([Meta_K, Ctrl_K], (v) => {
  if (v[0] || v[1]) isOpen.value = true
})
</script>

<template>
  <header class="sticky top-0 z-30 w-full border-b backdrop-blur-sm bg-white">
    <!-- 2xl以下：紧凑模式 -->
    <div class="block 2xl:hidden">
      <div class="h-[40px] w-full flex justify-between items-center px-2 gap-2">
        <!-- 左侧导航菜单 -->
        <div class="flex items-center gap-1 flex-1 min-w-0">
          <Navigation />
        </div>

        <!-- 右侧用户信息 -->
        <div class="flex items-center flex-shrink-0">
          <UserInfo />
        </div>
      </div>
    </div>

    <!-- 2xl以上：完整模式 -->
    <div class="hidden 2xl:block">
      <div class="h-[60px] w-full flex justify-between items-center px-4 gap-4">
        <!-- 左侧：Logo + 导航菜单 -->
        <div class="flex items-center gap-6 flex-1 min-w-0">
          <!-- Logo区域 -->
          <!-- <div class="flex items-center gap-2">
            <div class="w-8 h-8 bg-gray-900 rounded-md flex items-center justify-center">
              <span class="text-white text-sm font-bold">OLP</span>
            </div>
            <span class="font-semibold text-gray-900">Online Learning Platform</span>
          </div> -->

          <!-- 导航菜单 -->
          <Navigation class="ml-[-8px]"/>
        </div>

        <!-- 右侧：搜索 + 用户信息 -->
        <div class="flex items-center gap-4 flex-shrink-0">
          <!-- 搜索框 -->
          <!-- <div class="relative">
            <input
              type="text"
              placeholder="Search..."
              class="w-64 h-8 px-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div> -->

          <!-- 用户信息 -->
          <UserInfo />
        </div>
      </div>
    </div>
  </header>
  <Dialog v-model:open="isOpen">
    <DialogContent class="p-0">
      <Command>
        <CommandInput placeholder="Type a command or search..." />
        <CommandEmpty> No results found.</CommandEmpty>
        <CommandList @escape-key-down="isOpen = false">
          <CommandGroup heading="Links">
            <CommandItem
              v-for="item in docsConfig.mainNav"
              :key="item.title"
              :heading="item.title"
              :value="item.title"
              class="py-3"
              @select="handleSelectLink(item)"
            >
              <File class="mr-2 h-5 w-5" />
              <span>{{ item.title }}</span>
            </CommandItem>
          </CommandGroup>
          <CommandSeparator />
          <CommandGroup
            v-for="item in docsConfig.sidebarNav"
            :key="item.title"
            :heading="item.title"
          >
            <CommandItem
              v-for="subItem in item.items"
              :key="subItem.title"
              :heading="subItem.title"
              :value="subItem.title"
              class="py-3"
              @select="handleSelectLink(subItem)"
            >
              <Circle class="mr-2 h-4 w-4" />
              <span>{{ subItem.title }}</span>
            </CommandItem>
          </CommandGroup>
          <CommandSeparator />
          <CommandGroup heading="Theme">
            <CommandItem
              value="light-theme"
              class="py-3"
              @select="
                () => {
                  isDark = false
                  isOpen = false
                }
              "
            >
              <SunIcon class="mr-2 h-5 w-5" />
              <span>Light Theme</span>
            </CommandItem>
            <CommandItem
              value="dark-theme"
              class="py-3"
              @select="
                () => {
                  isDark = true
                  isOpen = false
                }
              "
            >
              <MoonIcon class="mr-2 h-5 w-5" />
              <span>Dark Theme</span>
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </Command>
    </DialogContent>
  </Dialog>
</template>
