import {
  Home,
  BellRing,
  LucideIcon,
  Search,
  Sparkles,
  Calendar,
  Settings2,
  Blocks,
  Trash2,
  MessageCircleQuestion,
  CirclePlay,
  MapPin,
  Layers,
  School,
  Wrench,
  Landmark,
  Bot
} from 'lucide-vue-next'

export const enum NavTypeEnum {
  MAIN = 1,
  SECONDARY = 2,
  TOOL = 3
}

// 统一的高度配置
export const LAYOUT_HEIGHTS = {
  HEADER: 60, // 主 Header 高度
  PAGE_HEADER: 52, // 页面内部 Header 高度
  SIDEBAR_ITEM: 40, // 侧边栏项目高度
  BUTTON: 32, // 标准按钮高度
} as const

export interface INav {
  title: string
  url: string
  description?: string
  route: string
  type?: NavTypeEnum
  icon: LucideIcon
  badge?: number
  isActive?: boolean
  subNavs?: INav[]
}

export const navs: INav[] = [
  {
    title: 'Home',
    url: '/',
    route: '/home',
    type: NavTypeEnum.MAIN,
    icon: Home,
    description:
      'A modal dialog that interrupts the user with important content and expects a response.'
  },
  // {
  //   title: 'Search',
  //   url: '#',
  //   route: '#',
  //   icon: Search,
  //   type: NavTypeEnum.TOOL
  // },
  {
    title: 'Assistant',
    url: '#',
    route: 'ai-assistant',
    // icon: Sparkles,
    icon: Bot,
    type: NavTypeEnum.TOOL
  },
  {
    title: 'Toolkit',
    url: '#',
    route: '/ai',
    icon: Wrench,
    type: NavTypeEnum.TOOL
  },
  {
    title: 'Online Learning',
    url: '/courses',
    route: '/courses',
    description: 'For sighted users to preview content available behind a link.',
    icon: School,
    type: NavTypeEnum.MAIN,
    subNavs: [
      {
        title: 'Content',
        url: '/content/center',
        route: '/content/center',
        description:
          'Discover a wide array of resources and materials to enhance your learning experience and knowledge depth.',
        icon: Home
      },
      {
        title: 'Onboarding',
        url: '/onboarding',
        route: '/onboarding',
        description:
          'Get started with comprehensive guidance and support to ensure a smooth transition into your new role or platform.',
        icon: Home
      },
      {
        title: 'Orientation',
        url: '/orientation',
        route: '/orientation',
        description:
          'Navigate through essential information and tools designed to acquaint you with our system and environment.',
        icon: Home
      },
      {
        title: 'Company Policy',
        url: '/policy',
        route: '/policy',
        description:
          'Access key guidelines and regulations that govern workplace conduct and organizational procedures.',
        icon: Home
      }
    ]
  },
  {
    title: 'MLC Training',
    url: '/training',
    route: '/training',
    description: 'For sighted users to preview content available behind a link.',
    icon: Landmark,
    type: NavTypeEnum.MAIN,
    subNavs: [
      {
        title: 'MLC Training',
        url: '/training/mlc-training',
        route: '/training/mlc-training',
        description:
          'Offers specialized training in multilingual and cultural skills, helping you succeed in diverse environments.',
        icon: Home
      },
      {
        title: 'Overseas Training',
        url: '/training/overseas',
        route: '/training/overseas',
        description:
          'Provides specialized courses for international business or job opportunities, helping you adapt to global environments and challenges.',
        icon: Home
      }
    ]
  },
  {
    title: 'Live Stream',
    url: '/live?type=stream-central',
    route: '/live',
    description:
      'Displays an indicator showing the completion progress of a task, typically displayed as a progress bar.',
    icon: CirclePlay,
    type: NavTypeEnum.MAIN
  },
  {
    title: 'Employee Development Plan',
    url: '/edp',
    route: '/edp',
    description: 'For sighted users to preview content available behind a link.',
    icon: MapPin,
    type: NavTypeEnum.MAIN,
    subNavs: [
      {
        title: 'Career Development Map',
        url: '/edp/career-develop-map',
        route: '/edp/career-develop-map',
        description:
          'Offers a personalized roadmap for career development, helping you plan your career and achieve long-term goals.',
        icon: Home
      },
      {
        title: 'Profession Profile',
        url: '/edp/profession-profile',
        route: '/edp/profession-profile',
        description:
          'Create and manage your professional profile to showcase your skills and experience, enhancing your career image.',
        icon: Home
      },
      {
        title: 'Knowledge Management',
        url: '/edp/knowledge',
        route: '/edp/knowledge',
        description:
          'Provides tools for effectively organizing, storing, and sharing information to improve work efficiency.',
        icon: Home
      },
      {
        title: 'Learning Map',
        url: '/edp/learning-map',
        route: '/edp/learning-map',
        description:
          'Customize learning paths and track your progress for a clear understanding of key points throughout your learning journey.',
        icon: Home
      },
      {
        title: 'My Study Plan',
        url: '/edp/study-plan',
        route: '/edp/study-plan',
        description:
          'Develop a personalized study plan to efficiently arrange your study time and achieve learning objectives.',
        icon: Home
      },
      {
        title: 'Training Need',
        url: '/edp/training-need',
        route: '/edp/training-need',
        description:
          'Develop a personalized study plan to efficiently arrange your study time and achieve learning objectives.',
        icon: Home
      },
      {
        title: 'My Study Report',
        url: '/edp/study-report',
        route: '/edp/study-report',
        description:
          'Provides study reports that analyze your performance and progress, helping you continuously optimize your learning strategies.',
        icon: Home
      }
    ]
  },
  // {
  //   title: 'My Center',
  //   url: '/my-center',
  //   route: '/my-center',
  //   description: 'For sighted users to preview content available behind a link.',
  //   icon: Layers,
  //   type: NavTypeEnum.SECONDARY
  // },
  {
    title: 'Calendar',
    url: '/calendar',
    route: '/calendar',
    icon: Calendar,
    type: NavTypeEnum.SECONDARY
  },
  {
    title: 'Messages',
    url: '/message/list',
    route: '/message/list',
    type: NavTypeEnum.SECONDARY,
    icon: BellRing,
    badge: 10
  },
  // {
  //   title: 'Settings',
  //   url: '#',
  //   route: '#',
  //   icon: Settings2,
  //   type: NavTypeEnum.SECONDARY
  // },
  // {
  //   title: 'Trash',
  //   url: '#',
  //   route: '#',
  //   icon: Trash2,
  //   type: NavTypeEnum.SECONDARY
  // },
  // {
  //   title: 'Help',
  //   url: '#',
  //   route: '#',
  //   icon: MessageCircleQuestion,
  //   type: NavTypeEnum.SECONDARY
  // }
]
