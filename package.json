{"name": "OLP", "version": "v3", "description": "Online Learning Platform", "author": "xingyu", "private": false, "scripts": {"i": "pnpm install", "local": "vite --mode env.local", "dev": "vite --mode dev", "test": "vite --mode test", "ts:check": "vue-tsc --noEmit", "build:local": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build", "build:dev": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode dev", "build:test": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode test", "build:uat": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode uat", "build:stage": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode stage", "build:prod": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode prod", "serve:dev": "vite preview --mode dev", "serve:prod": "vite preview --mode prod", "preview": "pnpm build:local && vite preview", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --fix \"./src/**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ", "prepare": "husky install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@form-create/designer": "^3.2.6", "@form-create/element-ui": "^3.2.11", "@icon-park/vue-next": "^1.4.2", "@iconify/iconify": "^3.1.1", "@internationalized/date": "^3.7.0", "@microsoft/fetch-event-source": "^2.0.1", "@radix-icons/vue": "^1.0.0", "@tailwindcss/vite": "^4.1.10", "@tanstack/vue-table": "^8.21.3", "@unovis/ts": "^1.5.2", "@unovis/vue": "^1.5.2", "@vee-validate/zod": "^4.15.0", "@videojs-player/vue": "^1.0.0", "@vueuse/core": "^10.9.0", "@vueuse/router": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "aplayer": "^1.10.1", "axios": "^1.6.8", "benz-amr-recorder": "^1.1.5", "bpmn-js-token-simulation": "^0.10.0", "camunda-bpmn-moddle": "^7.0.1", "chardet": "^2.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "countly-sdk-web": "^25.1.0", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "delay": "^6.0.0", "diagram-js": "^12.8.0", "docx-preview": "^0.1.15", "dplayer": "^1.27.1", "driver.js": "^1.3.1", "echarts": "^5.5.0", "echarts-wordcloud": "^2.1.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-vue": "^8.6.0", "fast-xml-parser": "^4.3.2", "file-type": "^19.2.0", "got": "^14.4.5", "highlight.js": "^11.9.0", "install": "^0.13.0", "js-base64": "^3.7.7", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.523.0", "magic-string": "^0.30.17", "markdown-it": "^14.1.0", "markmap-common": "^0.16.0", "markmap-lib": "^0.16.1", "markmap-toolbar": "^0.17.0", "markmap-view": "^0.16.0", "min-dash": "^4.1.1", "mitt": "^3.0.1", "moment": "^2.30.1", "nanoid": "^5.0.7", "nprogress": "^0.2.0", "p-queue": "^8.0.1", "pdfjs-dist": "^4.10.38", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "promise-queue-plus": "^1.2.2", "qrcode": "^1.5.3", "qs": "^6.12.0", "reka-ui": "^2.3.1", "scorm-again": "^1.7.1", "sortablejs": "^1.15.3", "spark-md5": "^3.0.2", "steady-xml": "^0.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "url": "^0.11.3", "v-viewer": "^3.0.11", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.0", "video.js": "^7.21.5", "viewerjs": "^1.11.7", "vue": "3.5.12", "vue-clipboard3": "^2.0.0", "vue-dompurify-html": "^4.1.4", "vue-i18n": "9.10.2", "vue-lazyload-next": "^0.0.2", "vue-pdf-embed": "^2.1.1", "vue-router": "^4.4.5", "vue-sonner": "^1.3.0", "vue-types": "^5.1.1", "vue3-pdfjs": "^0.1.6", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1", "xlsx": "^0.18.5", "xml-js": "^1.6.11", "zod": "^3.24.2"}, "devDependencies": {"@commitlint/cli": "^19.0.1", "@commitlint/config-conventional": "^19.0.0", "@iconify-json/gravity-ui": "^1.2.5", "@iconify-json/lucide": "^1.2.26", "@iconify-json/ph": "^1.2.2", "@iconify-json/radix-icons": "^1.2.2", "@iconify-json/ri": "^1.2.5", "@iconify-json/simple-icons": "^1.2.24", "@iconify-json/tabler": "^1.2.16", "@iconify/json": "^2.2.187", "@iconify/vue": "^4.3.0", "@intlify/unplugin-vue-i18n": "^2.0.0", "@purge-icons/generated": "^0.9.0", "@shikijs/transformers": "^2.3.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.21", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.12", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-legacy": "^5.3.1", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "autoprefixer": "^10.4.17", "bpmn-js": "8.10.0", "bpmn-js-properties-panel": "0.46.0", "consola": "^3.2.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.22.0", "husky": "^8.0.0", "lint-staged": "^15.2.2", "naive-ui": "^2.40.3", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "rimraf": "^5.0.5", "rollup": "^4.12.0", "sass": "^1.69.5", "shiki": "^2.3.2", "stylelint": "^16.2.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^14.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "tailwindcss": "^4.1.10", "terser": "^5.28.1", "typescript": "5.3.3", "unplugin-auto-import": "^0.16.7", "unplugin-icons": "^22.0.0", "unplugin-vue-components": "^0.25.2", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-ejs": "^1.7.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-top-level-await": "^1.4.4", "vue-eslint-parser": "^9.3.2", "vue-tsc": "^1.8.27"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://gitee.com/yudaocode/yudao-ui-admin-vue3"}, "bugs": {"url": "https://gitee.com/yudaocode/yudao-ui-admin-vue3/issues"}, "homepage": "https://gitee.com/yudaocode/yudao-ui-admin-vue3", "web-types": "./web-types.json", "engines": {"node": ">= 16.0.0", "pnpm": ">=8.6.0"}, "lint-staged": {"*.{js,vue,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.{html,css,less,scss,md}": ["prettier --write"]}}