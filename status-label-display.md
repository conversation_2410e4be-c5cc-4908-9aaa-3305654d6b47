# 状态 Label 显示修改

## 修改内容

### 1. 添加了 `getStatusLabel` 函数
```javascript
/** 根据status获取对应的label */
const getStatusLabel = (status: number) => {
  const found = statusOptions.value.find((item) => item.value === status)
  return found ? found.label : 'Unknown'
}
```

### 2. 修改表格中的状态显示
```vue
<!-- 修改前 -->
<Badge variant="secondary">
  {{ TrainingNeedStatusEnum[trainingNeed.status] }}
</Badge>

<!-- 修改后 -->
<Badge variant="secondary">
  {{ getStatusLabel(trainingNeed.status) }}
</Badge>
```

## 显示效果对比

### 修改前
- 状态值 `1` 显示为：`DRAFT`
- 状态值 `2` 显示为：`SUBMITTED`  
- 状态值 `3` 显示为：`APPROVED`
- 状态值 `4` 显示为：`REJECTED`

### 修改后
- 状态值 `1` 显示为：`Draft`
- 状态值 `2` 显示为：`Submitted`
- 状态值 `3` 显示为：`Approved`
- 状态值 `4` 显示为：`Rejected`

## 状态选项配置
```javascript
const statusOptions = ref<Option[]>([
  { label: 'Draft', value: TrainingNeedStatusEnum.DRAFT },
  { label: 'Submitted', value: TrainingNeedStatusEnum.SUBMITTED },
  { label: 'Approved', value: TrainingNeedStatusEnum.APPROVED },
  { label: 'Rejected', value: TrainingNeedStatusEnum.REJECTED }
])
```

## 函数工作原理

1. **接收状态值**：函数接收数字类型的状态值（如 1, 2, 3, 4）
2. **查找匹配项**：在 `statusOptions` 数组中查找 `value` 匹配的项
3. **返回 label**：如果找到匹配项，返回对应的 `label`；否则返回 'Unknown'

## 优势

1. **用户友好**：显示更易读的文本而不是枚举键名
2. **一致性**：与 `getTrainingTypeLabel` 函数保持一致的模式
3. **可维护性**：如果需要修改显示文本，只需修改 `statusOptions` 数组
4. **容错性**：对于未知状态值，显示 'Unknown' 而不是报错

## 测试用例

- 状态为 Draft (1) → 显示 "Draft"
- 状态为 Submitted (2) → 显示 "Submitted"  
- 状态为 Approved (3) → 显示 "Approved"
- 状态为 Rejected (4) → 显示 "Rejected"
- 状态为未知值 (99) → 显示 "Unknown"
