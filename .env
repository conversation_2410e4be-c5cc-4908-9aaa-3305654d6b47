# 标题
VITE_APP_TITLE=Online Learning Platform

# 项目本地运行端口号
VITE_PORT=81

# open 运行 npm run dev 时自动打开浏览器
VITE_OPEN=true

# 租户开关
VITE_APP_TENANT_ENABLE=true

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=true

# 文档地址的开关
VITE_APP_DOCALERT_ENABLE=true

#VITE_PREVIEW_PREFIX='https://test.portal.v2.olp.credat.com.cn/admin-api/preview/onlinePreview'
#VITE_PREVIEW_PREFIX='https://test.portal.v2.olp.credat.com.cn/admin-api/preview/onlinePreview/?url=aHR0cDovLzEwLjI0OC4xOC4xMTo5MDAwL3Nhcy8yMDI1MDIxMS80MDY0OTdiZS0yMTc0LTQ0NzctOTI4MS0zNDg1MjEyOGRkODEucGRm&systemType=2&officePreviewType=pdf&directReturnURL=pdf'
#VITE_PREVIEW_PREFIX='https://test.portal.v2.olp.credat.com.cn/admin-api/preview/onlinePreview?url=aHR0cHM6Ly9yZXNvdXJjZS5jcmVkYXQuY29tLmNuL3Nhcy8yMDI1MDIxMS84MzYyNzAwYS0zMTZmLTQ2NzItODVhZC1hY2NjNWY4MWViNDQucGRm&watermarkTxt=Super%20Admin&officePreviewType=pdf&directReturnURL=&pageNum=1&keyword='

# 百度统计
VITE_APP_BAIDU_CODE=a1ff8825baa73c3a78eb96aa40325abc1111

# 默认账户密码
VITE_APP_DEFAULT_LOGIN_TENANT = olp
VITE_APP_DEFAULT_LOGIN_USERNAME = admin
VITE_APP_DEFAULT_LOGIN_PASSWORD = ''

#对象存储器编码
VITE_STORAGE_CODE=s3_minio_1
